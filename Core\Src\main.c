/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * <h2><center>&copy; Copyright (c) 2025 STMicroelectronics.
 * All rights reserved.</center></h2>
 *
 * This software component is licensed by ST under BSD 3-Clause license,
 * the "License"; You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                        opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "cmsis_os.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "fw_framework.h"
#include "fw_error.h"
#include "fw_gpio.h"
#include "fw_rtos.h"
#include <stdio.h>
#include <string.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
/* LED应用相关变量 */
static fw_gpio_handle_t g_led_handle;
static fw_task_handle_t g_led_task_handle;
static fw_task_handle_t g_demo_task_handle;

/* 框架状态变量 */
static bool g_framework_initialized = false;
static uint32_t g_demo_counter = 0;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void MX_FREERTOS_Init(void);
/* USER CODE BEGIN PFP */
/* 框架初始化函数 */
static void Framework_Init(void);
static void LED_Application_Init(void);
static void UART_Printf_Init(void);

/* 任务函数 */
static void LED_Task_Function(void *argument);
static void Demo_Task_Function(void *argument);

/* 工具函数 */
static void Print_Framework_Status(void);
static void Print_System_Info(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief UART重定向printf函数
 */
int _write(int file, char *ptr, int len)
{
  HAL_UART_Transmit(&huart1, (uint8_t *)ptr, len, HAL_MAX_DELAY);
  return len;
}

/**
 * @brief 自定义日志输出回调函数
 */
void Custom_Log_Output(const fw_log_message_t *message)
{
  if (message == NULL)
    return;

  printf("[%lu] %s %s: %s\r\n",
         message->timestamp,
         fw_logger_get_level_string(message->level),
         message->tag,
         message->message);
}

/* USER CODE END 0 */

/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART1_UART_Init();
  /* USER CODE BEGIN 2 */

  /* 初始化UART printf重定向 */
  UART_Printf_Init();

  /* 初始化框架 */
  Framework_Init();

  /* 初始化LED应用 */
  LED_Application_Init();

  /* 打印系统信息 */
  Print_System_Info();

  /* 打印框架状态 */
  Print_Framework_Status();

  /* USER CODE END 2 */

  /* Init scheduler */
  osKernelInitialize(); /* Call init function for freertos objects (in freertos.c) */
  MX_FREERTOS_Init();
  /* Start scheduler */
  osKernelStart();

  /* We should never get here as control is now taken by the scheduler */
  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
   */
  HAL_PWREx_ControlVoltageScaling(PWR_REGULATOR_VOLTAGE_SCALE1);
  /** Initializes the RCC Oscillators according to the specified parameters
   * in the RCC_OscInitTypeDef structure.
   */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }
  /** Initializes the CPU, AHB and APB buses clocks
   */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  /** Initializes the peripherals clocks
   */
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1;
  PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK2;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief 初始化UART printf重定向
 */
static void UART_Printf_Init(void)
{
  printf("\r\n");
  printf("=================================================\r\n");
  printf("    STM32G474 Embedded Development Framework Demo\r\n");
  printf("=================================================\r\n");
  printf("System starting...\r\n");
}

/**
 * @brief 初始化框架
 */
static void Framework_Init(void)
{
  fw_framework_result_t result;
  fw_framework_config_t config;

  printf("Initializing framework...\r\n");

  /* 配置框架 */
  config = (fw_framework_config_t){
      .enable_logger = true,
      .log_level = FW_LOG_LEVEL_DEBUG,
      .log_output_callback = Custom_Log_Output,
      .enable_error_handler = true,
      .error_callback = fw_error_default_handler,
      .enable_gpio = true,
      .enable_uart = true,
      .enable_timer = false,
      .enable_adc = false,
      .enable_rtos_wrapper = true,
      .enable_app_framework = true};

  /* 初始化框架 */
  result = fw_framework_init(&config);
  if (result != FW_FRAMEWORK_OK)
  {
    printf("Error: Framework init failed (error code: %d)\r\n", result);
    Error_Handler();
    return;
  }

  printf("Framework initialized successfully!\r\n");
  g_framework_initialized = true;

  /* Test log system */
  FW_LOG_INFO("MAIN", "Framework initialization completed, starting system test");
  FW_LOG_DEBUG("MAIN", "Debug level log test");
  FW_LOG_WARN("MAIN", "Warning level log test");
}

/**
 * @brief 初始化LED应用
 */
static void LED_Application_Init(void)
{
  fw_gpio_config_t led_config;
  fw_task_config_t led_task_config, demo_task_config;
  fw_rtos_result_t rtos_result;

  FW_LOG_INFO("MAIN", "Initializing LED application...");

  /* 配置LED GPIO */
  led_config = (fw_gpio_config_t){
      .port = GPIOA,
      .pin = GPIO_PIN_5,
      .mode = FW_GPIO_MODE_OUTPUT_PP,
      .pull = FW_GPIO_NOPULL,
      .speed = FW_GPIO_SPEED_LOW,
      .alternate = 0};

  g_led_handle = (fw_gpio_handle_t){
      .config = led_config,
      .initialized = false,
      .name = "LED"};

  /* 初始化LED GPIO */
  fw_gpio_result_t gpio_result = fw_gpio_pin_init(&g_led_handle);
  if (gpio_result != FW_GPIO_OK)
  {
    FW_ERROR_REPORT(FW_ERROR_HW_GPIO_INIT_FAILED, "MAIN", "LED GPIO initialization failed");
    return;
  }

  /* 创建LED任务 */
  led_task_config = (fw_task_config_t){
      .name = "LED_Task",
      .function = LED_Task_Function,
      .priority = FW_TASK_PRIORITY_LOW,
      .stack_size = 256,
      .argument = NULL};

  g_led_task_handle = (fw_task_handle_t){
      .config = led_task_config,
      .initialized = false,
      .state = FW_TASK_STATE_INACTIVE};

  rtos_result = fw_task_create(&g_led_task_handle);
  if (rtos_result != FW_RTOS_OK)
  {
    FW_ERROR_REPORT(FW_ERROR_SYSTEM_TASK_CREATION_FAILED, "MAIN", "LED task creation failed");
    return;
  }

  /* 创建演示任务 */
  demo_task_config = (fw_task_config_t){
      .name = "Demo_Task",
      .function = Demo_Task_Function,
      .priority = FW_TASK_PRIORITY_NORMAL,
      .stack_size = 512,
      .argument = NULL};

  g_demo_task_handle = (fw_task_handle_t){
      .config = demo_task_config,
      .initialized = false,
      .state = FW_TASK_STATE_INACTIVE};

  rtos_result = fw_task_create(&g_demo_task_handle);
  if (rtos_result != FW_RTOS_OK)
  {
    FW_ERROR_REPORT(FW_ERROR_SYSTEM_TASK_CREATION_FAILED, "MAIN", "Demo task creation failed");
    return;
  }

  FW_LOG_INFO("MAIN", "LED application initialization completed");
}

/**
 * @brief LED任务函数
 */
static void LED_Task_Function(void *argument)
{
  uint32_t led_state = 0; /* 0=关闭, 1=常亮, 2=慢闪, 3=快闪 */
  uint32_t last_toggle_time = 0;
  uint32_t blink_period = 1000; /* 闪烁周期(ms) */
  bool led_physical_state = false;

  FW_LOG_INFO("LED_TASK", "LED task started");

  while (1)
  {
    uint32_t current_time = fw_rtos_get_tick_count();

    switch (led_state)
    {
    case 0: /* LED off */
      if (led_physical_state)
      {
        fw_gpio_pin_write(&g_led_handle, FW_GPIO_PIN_RESET);
        led_physical_state = false;
        FW_LOG_DEBUG("LED_TASK", "LED off");
      }
      break;

    case 1: /* LED on */
      if (!led_physical_state)
      {
        fw_gpio_pin_write(&g_led_handle, FW_GPIO_PIN_SET);
        led_physical_state = true;
        FW_LOG_DEBUG("LED_TASK", "LED常亮");
      }
      break;

    case 2:                /* 慢速闪烁 */
      blink_period = 1000; /* 1秒 */
      if (current_time - last_toggle_time >= blink_period)
      {
        fw_gpio_pin_toggle(&g_led_handle);
        led_physical_state = !led_physical_state;
        last_toggle_time = current_time;
        FW_LOG_DEBUG("LED_TASK", "LED slow blink: %s", led_physical_state ? "ON" : "OFF");
      }
      break;

    case 3:               /* 快速闪烁 */
      blink_period = 200; /* 200ms */
      if (current_time - last_toggle_time >= blink_period)
      {
        fw_gpio_pin_toggle(&g_led_handle);
        led_physical_state = !led_physical_state;
        last_toggle_time = current_time;
        FW_LOG_DEBUG("LED_TASK", "LED fast blink: %s", led_physical_state ? "ON" : "OFF");
      }
      break;
    }

    /* Check if state needs to be switched (controlled by demo task) */
    led_state = g_demo_counter % 4;

    /* Task delay */
    fw_task_delay(50);
  }
}

/**
 * @brief 演示任务函数
 */
static void Demo_Task_Function(void *argument)
{
  uint32_t last_demo_time = 0;
  uint32_t last_status_time = 0;

  FW_LOG_INFO("DEMO_TASK", "Demo task started");

  while (1)
  {
    uint32_t current_time = fw_rtos_get_tick_count();

    /* Switch LED mode every 5 seconds */
    if (current_time - last_demo_time >= 5000)
    {
      last_demo_time = current_time;
      g_demo_counter++;

      const char *mode_names[] = {"Off", "On", "Slow Blink", "Fast Blink"};
      uint32_t mode = g_demo_counter % 4;

      FW_LOG_INFO("DEMO_TASK", "Switch LED mode: %s", mode_names[mode]);

      /* Test error reporting system */
      if (g_demo_counter % 10 == 5)
      {
        FW_ERROR_REPORT_WARNING(FW_ERROR_APP_INVALID_STATE, "DEMO_TASK",
                                "This is a test warning (count: %lu)", g_demo_counter);
      }
    }

    /* Print system status every 30 seconds */
    if (current_time - last_status_time >= 30000)
    {
      last_status_time = current_time;
      Print_Framework_Status();

      /* Execute framework runtime check */
      fw_framework_runtime_check();
    }

    /* Task delay */
    fw_task_delay(1000);
  }
}

/**
 * @brief 打印系统信息
 */
static void Print_System_Info(void)
{
  printf("\r\n");
  printf("=== System Information ===\r\n");
  printf("MCU: STM32G474xC\r\n");
  printf("Framework version: %s\r\n", fw_framework_get_version());
  printf("System clock: %lu Hz\r\n", HAL_RCC_GetSysClockFreq());
  printf("HCLK: %lu Hz\r\n", HAL_RCC_GetHCLKFreq());
  printf("PCLK1: %lu Hz\r\n", HAL_RCC_GetPCLK1Freq());
  printf("PCLK2: %lu Hz\r\n", HAL_RCC_GetPCLK2Freq());
  printf("FreeRTOS Tick: 1000 Hz\r\n");
  printf("=================\r\n");
}

/**
 * @brief 打印框架状态
 */
static void Print_Framework_Status(void)
{
  char status_buffer[1024];
  uint32_t heap_free, heap_total;
  uint32_t error_count;

  if (!g_framework_initialized)
  {
    printf("Framework not initialized\r\n");
    return;
  }

  printf("\r\n");
  printf("=== Framework Status ===\r\n");

  /* Get framework status information */
  if (fw_framework_get_status_info(status_buffer, sizeof(status_buffer)) == FW_FRAMEWORK_OK)
  {
    printf("%s", status_buffer);
  }

  /* Get memory information */
  if (fw_framework_get_memory_info(&heap_free, &heap_total) == FW_FRAMEWORK_OK)
  {
    printf("Current free heap memory: %lu bytes\r\n", heap_free);
  }

  /* Get error statistics */
  error_count = fw_error_get_total_count();
  printf("Total error count: %lu\r\n", error_count);
  printf("Error level statistics:\r\n");
  printf("  - Info: %lu\r\n", fw_error_get_count_by_severity(FW_ERROR_SEVERITY_INFO));
  printf("  - Warning: %lu\r\n", fw_error_get_count_by_severity(FW_ERROR_SEVERITY_WARNING));
  printf("  - Error: %lu\r\n", fw_error_get_count_by_severity(FW_ERROR_SEVERITY_ERROR));
  printf("  - Critical: %lu\r\n", fw_error_get_count_by_severity(FW_ERROR_SEVERITY_CRITICAL));

  /* Get task status */
  fw_task_state_t led_task_state, demo_task_state;
  if (fw_task_get_state(&g_led_task_handle, &led_task_state) == FW_RTOS_OK)
  {
    printf("LED task status: %d\r\n", led_task_state);
  }
  if (fw_task_get_state(&g_demo_task_handle, &demo_task_state) == FW_RTOS_OK)
  {
    printf("Demo task status: %d\r\n", demo_task_state);
  }

  printf("Demo counter: %lu\r\n", g_demo_counter);
  printf("Current LED mode: %s\r\n",
         (g_demo_counter % 4 == 0) ? "Off" : (g_demo_counter % 4 == 1) ? "On"
                                          : (g_demo_counter % 4 == 2)   ? "Slow Blink"
                                                                        : "Fast Blink");

  printf("================\r\n");
}

/* USER CODE END 4 */

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef USE_FULL_ASSERT
/**
 * @brief  Reports the name of the source file and the source line number
 *         where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
