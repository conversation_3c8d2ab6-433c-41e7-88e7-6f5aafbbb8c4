/**
  ******************************************************************************
  * @file    led_blink_app.h
  * @brief   LED闪烁应用头文件
  ******************************************************************************
  * @attention
  *
  * 此文件包含LED闪烁应用的定义和函数，展示框架的使用方法。
  *
  ******************************************************************************
  */

#ifndef __LED_BLINK_APP_H__
#define __LED_BLINK_APP_H__

#ifdef __cplusplus
extern "C" {
#endif

/* 包含文件 ------------------------------------------------------------------*/
#include "fw_app.h"
#include "fw_gpio.h"
#include <stdint.h>
#include <stdbool.h>

/* 导出类型 ------------------------------------------------------------------*/

/**
 * @brief LED状态枚举
 */
typedef enum {
    LED_STATE_OFF = 0,
    LED_STATE_ON,
    LED_STATE_BLINKING_SLOW,
    LED_STATE_BLINKING_FAST,
    LED_STATE_ERROR
} led_state_t;

/**
 * @brief LED事件ID枚举
 */
typedef enum {
    LED_EVENT_TURN_ON = 1,
    LED_EVENT_TURN_OFF,
    LED_EVENT_START_SLOW_BLINK,
    LED_EVENT_START_FAST_BLINK,
    LED_EVENT_TIMER_TICK,
    LED_EVENT_ERROR_OCCURRED
} led_event_id_t;

/**
 * @brief LED配置结构体
 */
typedef struct {
    fw_gpio_handle_t gpio_handle;
    uint32_t slow_blink_period_ms;
    uint32_t fast_blink_period_ms;
    bool auto_start;
} led_config_t;

/**
 * @brief LED应用上下文结构体
 */
typedef struct {
    fw_app_task_t app_task;
    fw_state_machine_t state_machine;
    led_config_t config;
    led_state_t current_led_state;
    uint32_t last_toggle_time;
    uint32_t blink_period;
    bool led_physical_state;
    bool initialized;
} led_app_context_t;

/**
 * @brief LED应用结果枚举
 */
typedef enum {
    LED_APP_OK = 0,
    LED_APP_ERROR,
    LED_APP_INVALID_PARAM,
    LED_APP_NOT_INITIALIZED,
    LED_APP_GPIO_ERROR
} led_app_result_t;

/* 导出常量 ------------------------------------------------------------------*/

/* 默认闪烁周期 */
#define LED_DEFAULT_SLOW_BLINK_PERIOD   1000    /* 1秒 */
#define LED_DEFAULT_FAST_BLINK_PERIOD   200     /* 200毫秒 */

/* LED任务配置 */
#define LED_TASK_NAME                   "LED_Task"
#define LED_TASK_PRIORITY               FW_TASK_PRIORITY_LOW
#define LED_TASK_STACK_SIZE             256

/* LED GPIO配置 */
#define LED_GPIO_PORT                   FW_GPIO_PORT_A
#define LED_GPIO_PIN                    FW_GPIO_PIN_5
#define LED_GPIO_NAME                   "LED"

/* 导出宏 --------------------------------------------------------------------*/

/**
 * @brief LED配置初始化宏
 */
#define LED_CONFIG_INIT(gpio_port, gpio_pin, gpio_name, slow_period, fast_period, auto_start_flag) \
    { \
        .gpio_handle = { \
            .config = FW_GPIO_CONFIG_OUTPUT(gpio_port, gpio_pin, gpio_name), \
            .initialized = false, \
            .name = gpio_name \
        }, \
        .slow_blink_period_ms = slow_period, \
        .fast_blink_period_ms = fast_period, \
        .auto_start = auto_start_flag \
    }

/**
 * @brief 默认LED配置宏
 */
#define LED_CONFIG_DEFAULT() \
    LED_CONFIG_INIT(LED_GPIO_PORT, LED_GPIO_PIN, LED_GPIO_NAME, \
                   LED_DEFAULT_SLOW_BLINK_PERIOD, LED_DEFAULT_FAST_BLINK_PERIOD, true)

/* 导出函数原型 --------------------------------------------------------------*/

/**
 * @brief 初始化LED应用
 * @param led_ctx LED应用上下文指针
 * @param config LED配置指针
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_init(led_app_context_t* led_ctx, const led_config_t* config);

/**
 * @brief 反初始化LED应用
 * @param led_ctx LED应用上下文指针
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_deinit(led_app_context_t* led_ctx);

/**
 * @brief 启动LED应用任务
 * @param led_ctx LED应用上下文指针
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_start(led_app_context_t* led_ctx);

/**
 * @brief 停止LED应用任务
 * @param led_ctx LED应用上下文指针
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_stop(led_app_context_t* led_ctx);

/**
 * @brief 打开LED
 * @param led_ctx LED应用上下文指针
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_turn_on(led_app_context_t* led_ctx);

/**
 * @brief 关闭LED
 * @param led_ctx LED应用上下文指针
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_turn_off(led_app_context_t* led_ctx);

/**
 * @brief 开始慢速闪烁
 * @param led_ctx LED应用上下文指针
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_start_slow_blink(led_app_context_t* led_ctx);

/**
 * @brief 开始快速闪烁
 * @param led_ctx LED应用上下文指针
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_start_fast_blink(led_app_context_t* led_ctx);

/**
 * @brief 获取LED当前状态
 * @param led_ctx LED应用上下文指针
 * @retval led_state_t LED状态
 */
led_state_t led_app_get_state(const led_app_context_t* led_ctx);

/**
 * @brief 获取LED物理状态
 * @param led_ctx LED应用上下文指针
 * @retval bool LED物理状态（true=亮，false=灭）
 */
bool led_app_get_physical_state(const led_app_context_t* led_ctx);

/**
 * @brief 设置慢速闪烁周期
 * @param led_ctx LED应用上下文指针
 * @param period_ms 周期（毫秒）
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_set_slow_blink_period(led_app_context_t* led_ctx, uint32_t period_ms);

/**
 * @brief 设置快速闪烁周期
 * @param led_ctx LED应用上下文指针
 * @param period_ms 周期（毫秒）
 * @retval led_app_result_t LED应用结果
 */
led_app_result_t led_app_set_fast_blink_period(led_app_context_t* led_ctx, uint32_t period_ms);

/**
 * @brief LED任务函数（内部使用）
 * @param argument 任务参数（led_app_context_t指针）
 */
void led_app_task_function(void* argument);

/**
 * @brief 创建默认LED应用实例
 * @retval led_app_context_t* LED应用上下文指针（静态分配）
 */
led_app_context_t* led_app_create_default_instance(void);

/**
 * @brief 获取LED应用示例的使用说明
 * @retval const char* 使用说明字符串
 */
const char* led_app_get_usage_info(void);

#ifdef __cplusplus
}
#endif

#endif /* __LED_BLINK_APP_H__ */
