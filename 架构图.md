```mermaid
graph TD
    A[应用层 Application Layer] --> B[中间件层 Middleware Layer]
    B --> C[硬件抽象层 HAL Layer]
    C --> D[驱动层 Driver Layer]
    
    A1[任务管理器<br/>Task Manager] --> A
    A2[状态机<br/>State Machine] --> A
    A3[事件处理器<br/>Event Handler] --> A
    A4[应用配置<br/>App Config] --> A
    
    B1[FreeRTOS封装<br/>RTOS Wrapper] --> B
    B2[消息队列<br/>Message Queue] --> B
    B3[定时器管理<br/>Timer Manager] --> B
    B4[内存管理<br/>Memory Manager] --> B
    
    C1[GPIO抽象<br/>GPIO HAL] --> C
    C2[UART抽象<br/>UART HAL] --> C
    C3[定时器抽象<br/>Timer HAL] --> C
    C4[ADC抽象<br/>ADC HAL] --> C
    
    D1[STM32 HAL Driver] --> D
    D2[CMSIS] --> D
    D3[FreeRTOS Core] --> D
    
    E[公共资源层 Common Layer]
    E1[日志系统<br/>Logger] --> E
    E2[错误处理<br/>Error Handler] --> E
    E3[配置管理<br/>Config Manager] --> E
    E4[工具函数<br/>Utilities] --> E
    
    A -.-> E
    B -.-> E
    C -.-> E
```

