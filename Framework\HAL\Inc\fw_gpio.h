/**
 ******************************************************************************
 * @file    fw_gpio.h
 * @brief   框架GPIO抽象层头文件
 ******************************************************************************
 * @attention
 *
 * 此文件包含STM32G474嵌入式开发框架的GPIO抽象层定义和函数。
 *
 ******************************************************************************
 */

#ifndef __FW_GPIO_H__
#define __FW_GPIO_H__

#ifdef __cplusplus
extern "C"
{
#endif

/* 包含文件 ------------------------------------------------------------------*/
#include "fw_config.h"
#include "fw_error.h"
#include "main.h"
#include <stdint.h>
#include <stdbool.h>

    /* 导出类型 ------------------------------------------------------------------*/

    /**
     * @brief GPIO引脚状态枚举
     */
    typedef enum
    {
        FW_GPIO_PIN_RESET = 0,
        FW_GPIO_PIN_SET = 1
    } fw_gpio_pin_state_t;

    /**
     * @brief GPIO引脚模式枚举
     */
    typedef enum
    {
        FW_GPIO_MODE_INPUT = 0,
        FW_GPIO_MODE_OUTPUT_PP,
        FW_GPIO_MODE_OUTPUT_OD,
        FW_GPIO_MODE_AF_PP,
        FW_GPIO_MODE_AF_OD,
        FW_GPIO_MODE_ANALOG,
        FW_GPIO_MODE_IT_RISING,
        FW_GPIO_MODE_IT_FALLING,
        FW_GPIO_MODE_IT_RISING_FALLING,
        FW_GPIO_MODE_EVT_RISING,
        FW_GPIO_MODE_EVT_FALLING,
        FW_GPIO_MODE_EVT_RISING_FALLING
    } fw_gpio_mode_t;

    /**
     * @brief GPIO上拉/下拉枚举
     */
    typedef enum
    {
        FW_GPIO_NOPULL = 0,
        FW_GPIO_PULLUP,
        FW_GPIO_PULLDOWN
    } fw_gpio_pull_t;

    /**
     * @brief GPIO speed enumeration
     */
    typedef enum
    {
        FW_GPIO_SPEED_LOW = 0,
        FW_GPIO_SPEED_MEDIUM,
        FW_GPIO_SPEED_HIGH,
        FW_GPIO_SPEED_VERY_HIGH
    } fw_gpio_speed_t;

    /**
     * @brief GPIO pin configuration structure
     */
    typedef struct
    {
        GPIO_TypeDef *port;
        uint16_t pin;
        fw_gpio_mode_t mode;
        fw_gpio_pull_t pull;
        fw_gpio_speed_t speed;
        uint32_t alternate;
    } fw_gpio_config_t;

    /**
     * @brief GPIO pin handle structure
     */
    typedef struct
    {
        fw_gpio_config_t config;
        bool initialized;
        const char *name;
    } fw_gpio_handle_t;

    /**
     * @brief GPIO interrupt callback function type
     */
    typedef void (*fw_gpio_interrupt_callback_t)(fw_gpio_handle_t *handle);

    /**
     * @brief GPIO result enumeration
     */
    typedef enum
    {
        FW_GPIO_OK = 0,
        FW_GPIO_ERROR,
        FW_GPIO_INVALID_PARAM,
        FW_GPIO_NOT_INITIALIZED,
        FW_GPIO_ALREADY_INITIALIZED
    } fw_gpio_result_t;

/* Exported constants --------------------------------------------------------*/

/* Maximum number of GPIO interrupt callbacks */
#define FW_GPIO_MAX_INTERRUPT_CALLBACKS 16

/* GPIO port definitions */
#define FW_GPIO_PORT_A GPIOA
#define FW_GPIO_PORT_B GPIOB
#define FW_GPIO_PORT_C GPIOC
#define FW_GPIO_PORT_D GPIOD
#define FW_GPIO_PORT_E GPIOE
#define FW_GPIO_PORT_F GPIOF
#define FW_GPIO_PORT_G GPIOG

/* GPIO pin definitions */
#define FW_GPIO_PIN_0 GPIO_PIN_0
#define FW_GPIO_PIN_1 GPIO_PIN_1
#define FW_GPIO_PIN_2 GPIO_PIN_2
#define FW_GPIO_PIN_3 GPIO_PIN_3
#define FW_GPIO_PIN_4 GPIO_PIN_4
#define FW_GPIO_PIN_5 GPIO_PIN_5
#define FW_GPIO_PIN_6 GPIO_PIN_6
#define FW_GPIO_PIN_7 GPIO_PIN_7
#define FW_GPIO_PIN_8 GPIO_PIN_8
#define FW_GPIO_PIN_9 GPIO_PIN_9
#define FW_GPIO_PIN_10 GPIO_PIN_10
#define FW_GPIO_PIN_11 GPIO_PIN_11
#define FW_GPIO_PIN_12 GPIO_PIN_12
#define FW_GPIO_PIN_13 GPIO_PIN_13
#define FW_GPIO_PIN_14 GPIO_PIN_14
#define FW_GPIO_PIN_15 GPIO_PIN_15

/* Exported macro ------------------------------------------------------------*/

/**
 * @brief GPIO pin configuration helper macros
 */
#define FW_GPIO_CONFIG_OUTPUT(port, pin, name) \
    {.port = port, .pin = pin, .mode = FW_GPIO_MODE_OUTPUT_PP, .pull = FW_GPIO_NOPULL, .speed = FW_GPIO_SPEED_LOW, .alternate = 0}

#define FW_GPIO_CONFIG_INPUT(port, pin, pull, name) \
    {.port = port, .pin = pin, .mode = FW_GPIO_MODE_INPUT, .pull = pull, .speed = FW_GPIO_SPEED_LOW, .alternate = 0}

#define FW_GPIO_CONFIG_INTERRUPT(port, pin, mode, pull, name) \
    {.port = port, .pin = pin, .mode = mode, .pull = pull, .speed = FW_GPIO_SPEED_LOW, .alternate = 0}

/**
 * @brief GPIO handle initialization macro
 */
#define FW_GPIO_HANDLE_INIT(config, pin_name) \
    {.config = config, .initialized = false, .name = pin_name}

    /* Exported functions prototypes ---------------------------------------------*/

    /**
     * @brief Initialize GPIO abstraction layer
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_init(void);

    /**
     * @brief Deinitialize GPIO abstraction layer
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_deinit(void);

    /**
     * @brief Initialize a GPIO pin
     * @param handle Pointer to GPIO handle
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_pin_init(fw_gpio_handle_t *handle);

    /**
     * @brief Deinitialize a GPIO pin
     * @param handle Pointer to GPIO handle
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_pin_deinit(fw_gpio_handle_t *handle);

    /**
     * @brief Write to a GPIO pin
     * @param handle Pointer to GPIO handle
     * @param state Pin state to write
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_pin_write(fw_gpio_handle_t *handle, fw_gpio_pin_state_t state);

    /**
     * @brief Read from a GPIO pin
     * @param handle Pointer to GPIO handle
     * @param state Pointer to store pin state
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_pin_read(fw_gpio_handle_t *handle, fw_gpio_pin_state_t *state);

    /**
     * @brief Toggle a GPIO pin
     * @param handle Pointer to GPIO handle
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_pin_toggle(fw_gpio_handle_t *handle);

    /**
     * @brief Set GPIO pin interrupt callback
     * @param handle Pointer to GPIO handle
     * @param callback Interrupt callback function
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_pin_set_interrupt_callback(fw_gpio_handle_t *handle,
                                                        fw_gpio_interrupt_callback_t callback);

    /**
     * @brief Enable GPIO pin interrupt
     * @param handle Pointer to GPIO handle
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_pin_enable_interrupt(fw_gpio_handle_t *handle);

    /**
     * @brief Disable GPIO pin interrupt
     * @param handle Pointer to GPIO handle
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_pin_disable_interrupt(fw_gpio_handle_t *handle);

    /**
     * @brief GPIO interrupt handler (to be called from EXTI IRQ handlers)
     * @param pin GPIO pin number
     */
    void fw_gpio_interrupt_handler(uint16_t pin);

    /**
     * @brief Get GPIO pin name
     * @param handle Pointer to GPIO handle
     * @retval const char* Pin name
     */
    const char *fw_gpio_pin_get_name(fw_gpio_handle_t *handle);

    /**
     * @brief Check if GPIO pin is initialized
     * @param handle Pointer to GPIO handle
     * @retval bool True if initialized
     */
    bool fw_gpio_pin_is_initialized(fw_gpio_handle_t *handle);

    /**
     * @brief Get GPIO port clock enable function
     * @param port GPIO port
     * @retval fw_gpio_result_t GPIO result
     */
    fw_gpio_result_t fw_gpio_enable_port_clock(GPIO_TypeDef *port);

#ifdef __cplusplus
}
#endif

#endif /* __FW_GPIO_H__ */
