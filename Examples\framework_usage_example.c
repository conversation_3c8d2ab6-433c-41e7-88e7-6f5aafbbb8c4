/**
  ******************************************************************************
  * @file    framework_usage_example.c
  * @brief   框架使用示例
  ******************************************************************************
  * @attention
  *
  * 此文件展示如何在main.c中使用STM32G474嵌入式开发框架。
  * 包含框架初始化、LED闪烁应用创建和运行的完整示例。
  *
  ******************************************************************************
  */

/* 包含文件 ------------------------------------------------------------------*/
#include "fw_framework.h"
#include "led_blink_app.h"
#include "main.h"

/* 私有类型定义 --------------------------------------------------------------*/
/* 私有定义 ------------------------------------------------------------------*/
/* 私有宏 --------------------------------------------------------------------*/
/* 私有变量 ------------------------------------------------------------------*/

/**
 * @brief LED应用实例
 */
static led_app_context_t* g_led_app = NULL;

/* 私有函数原型 --------------------------------------------------------------*/
static void framework_usage_example_init(void);
static void framework_usage_example_run(void);
static void framework_usage_example_error_handler(void);

/* 示例函数 ------------------------------------------------------------------*/

/**
 * @brief 框架使用示例主函数
 * @note 此函数应该在main.c的USER CODE BEGIN 2区域调用
 */
void framework_usage_example_main(void)
{
    /* 初始化框架和应用 */
    framework_usage_example_init();
    
    /* 运行应用 */
    framework_usage_example_run();
}

/**
 * @brief 框架和应用初始化
 */
static void framework_usage_example_init(void)
{
    fw_framework_result_t fw_result;
    led_app_result_t led_result;
    fw_framework_config_t framework_config;
    
    /* 配置框架 */
    framework_config = (fw_framework_config_t)FW_FRAMEWORK_CONFIG_DEFAULT();
    
    /* 可以根据需要修改配置 */
    framework_config.log_level = FW_LOG_LEVEL_DEBUG;  /* 设置为调试级别 */
    
    /* 初始化框架 */
    fw_result = fw_framework_init(&framework_config);
    if (fw_result != FW_FRAMEWORK_OK) {
        /* 框架初始化失败，进入错误处理 */
        framework_usage_example_error_handler();
        return;
    }
    
    /* 框架初始化成功，记录日志 */
    FW_LOG_INFO("MAIN", "框架初始化成功");
    
    /* 创建LED应用实例 */
    g_led_app = led_app_create_default_instance();
    if (g_led_app == NULL) {
        FW_LOG_ERROR("MAIN", "创建LED应用失败");
        framework_usage_example_error_handler();
        return;
    }
    
    /* 启动LED应用任务 */
    led_result = led_app_start(g_led_app);
    if (led_result != LED_APP_OK) {
        FW_LOG_ERROR("MAIN", "启动LED应用失败: %d", led_result);
        framework_usage_example_error_handler();
        return;
    }
    
    FW_LOG_INFO("MAIN", "LED应用启动成功");
    
    /* 打印使用说明 */
    FW_LOG_INFO("MAIN", "%s", led_app_get_usage_info());
    
    /* 显示框架状态 */
    char status_info[512];
    fw_result = fw_framework_get_status_info(status_info, sizeof(status_info));
    if (fw_result == FW_FRAMEWORK_OK) {
        FW_LOG_INFO("MAIN", "框架状态:\n%s", status_info);
    }
}

/**
 * @brief 应用运行示例
 */
static void framework_usage_example_run(void)
{
    uint32_t demo_counter = 0;
    uint32_t last_demo_time = 0;
    uint32_t current_time;
    
    FW_LOG_INFO("MAIN", "开始运行LED控制演示");
    
    /* 主循环在FreeRTOS启动后不会执行，这里只是演示 */
    /* 实际的LED控制逻辑在LED应用任务中运行 */
    
    while (1) {
        current_time = fw_rtos_get_tick_count();
        
        /* 每5秒切换一次LED模式进行演示 */
        if (current_time - last_demo_time >= 5000) {
            last_demo_time = current_time;
            demo_counter++;
            
            switch (demo_counter % 4) {
                case 0:
                    FW_LOG_INFO("MAIN", "演示: 关闭LED");
                    led_app_turn_off(g_led_app);
                    break;
                    
                case 1:
                    FW_LOG_INFO("MAIN", "演示: 打开LED");
                    led_app_turn_on(g_led_app);
                    break;
                    
                case 2:
                    FW_LOG_INFO("MAIN", "演示: 慢速闪烁");
                    led_app_start_slow_blink(g_led_app);
                    break;
                    
                case 3:
                    FW_LOG_INFO("MAIN", "演示: 快速闪烁");
                    led_app_start_fast_blink(g_led_app);
                    break;
            }
            
            /* 显示LED当前状态 */
            led_state_t led_state = led_app_get_state(g_led_app);
            bool physical_state = led_app_get_physical_state(g_led_app);
            FW_LOG_INFO("MAIN", "LED状态: %d, 物理状态: %s", 
                       led_state, physical_state ? "ON" : "OFF");
        }
        
        /* 执行框架运行时检查 */
        if (current_time % 10000 == 0) {  /* 每10秒检查一次 */
            fw_framework_runtime_check();
        }
        
        /* 短暂延时 */
        fw_task_delay(100);
    }
}

/**
 * @brief 错误处理函数
 */
static void framework_usage_example_error_handler(void)
{
    /* 在错误情况下，尝试清理资源 */
    if (g_led_app != NULL) {
        led_app_stop(g_led_app);
        led_app_deinit(g_led_app);
    }
    
    /* 反初始化框架 */
    fw_framework_deinit();
    
    /* 进入无限循环，等待看门狗复位或手动复位 */
    while (1) {
        /* 可以在这里添加错误指示，比如闪烁错误LED */
        HAL_Delay(500);
    }
}

/**
 * @brief 获取框架使用示例说明
 * @retval const char* 使用说明字符串
 */
const char* framework_usage_example_get_info(void)
{
    return "STM32G474嵌入式开发框架使用示例:\n\n"
           "1. 在main.c的USER CODE BEGIN 2区域调用:\n"
           "   framework_usage_example_main();\n\n"
           "2. 确保在FreeRTOS启动前完成框架初始化\n\n"
           "3. 框架将自动:\n"
           "   - 初始化日志系统\n"
           "   - 初始化错误处理\n"
           "   - 初始化GPIO抽象层\n"
           "   - 初始化RTOS封装层\n"
           "   - 创建并启动LED闪烁应用\n\n"
           "4. LED将按以下模式循环演示:\n"
           "   - 关闭 -> 常亮 -> 慢速闪烁 -> 快速闪烁\n\n"
           "5. 可以通过UART查看日志输出\n\n"
           "6. 框架提供完整的错误处理和状态监控\n";
}

/**
 * @brief 在main.c中的集成示例
 * @note 以下代码展示如何在main.c中集成框架
 */
#if 0  /* 示例代码，不编译 */

/* 在main.c的USER CODE BEGIN Includes区域添加 */
#include "framework_usage_example.h"

int main(void)
{
    /* MCU Configuration--------------------------------------------------------*/
    
    /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
    HAL_Init();
    
    /* Configure the system clock */
    SystemClock_Config();
    
    /* Initialize all configured peripherals */
    MX_GPIO_Init();
    MX_USART1_UART_Init();
    
    /* USER CODE BEGIN 2 */
    
    /* 初始化并运行框架示例 */
    framework_usage_example_main();
    
    /* USER CODE END 2 */
    
    /* Init scheduler */
    osKernelInitialize();
    MX_FREERTOS_Init();
    
    /* Start scheduler */
    osKernelStart();
    
    /* We should never get here as control is now taken by the scheduler */
    while (1)
    {
        /* USER CODE BEGIN WHILE */
        /* USER CODE END WHILE */
        
        /* USER CODE BEGIN 3 */
        /* USER CODE END 3 */
    }
}

#endif /* 示例代码结束 */
