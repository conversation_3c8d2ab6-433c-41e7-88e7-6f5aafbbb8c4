# STM32G474 可扩展嵌入式开发框架

## 框架概述

这是一个为STM32G474微控制器设计的分层嵌入式开发框架，旨在提供可扩展、可维护的代码结构。

## 框架架构

### 1. 应用层 (Application Layer)
- **位置**: `Framework/Application/`
- **功能**: 业务逻辑实现，具体的应用功能
- **组件**:
  - 任务管理器 (Task Manager)
  - 状态机 (State Machine)
  - 事件处理器 (Event Handler)
  - 应用配置 (App Config)

### 2. 中间件层 (Middleware Layer)
- **位置**: `Framework/Middleware/`
- **功能**: 系统服务和抽象接口
- **组件**:
  - FreeRTOS封装 (RTOS Wrapper)
  - 消息队列 (Message Queue)
  - 定时器管理 (Timer Manager)
  - 内存管理 (Memory Manager)

### 3. 硬件抽象层 (HAL Layer)
- **位置**: `Framework/HAL/`
- **功能**: 硬件接口抽象，屏蔽底层硬件差异
- **组件**:
  - GPIO抽象 (GPIO HAL)
  - UART抽象 (UART HAL)
  - 定时器抽象 (Timer HAL)
  - ADC抽象 (ADC HAL)

### 4. 公共资源层 (Common Layer)
- **位置**: `Framework/Common/`
- **功能**: 通用工具和服务
- **组件**:
  - 日志系统 (Logger)
  - 错误处理 (Error Handler)
  - 配置管理 (Config Manager)
  - 工具函数 (Utilities)

### 5. 驱动层 (Driver Layer)
- **位置**: 使用现有的 `Drivers/` 和 `Middlewares/`
- **功能**: 底层驱动和RTOS内核

## 目录结构

```
STM32G4/
├── Framework/
│   ├── Application/
│   │   ├── Inc/
│   │   ├── Src/
│   │   └── Tasks/
│   ├── Middleware/
│   │   ├── Inc/
│   │   ├── Src/
│   │   └── RTOS/
│   ├── HAL/
│   │   ├── Inc/
│   │   └── Src/
│   ├── Common/
│   │   ├── Inc/
│   │   └── Src/
│   └── Config/
├── Core/
├── Drivers/
├── Middlewares/
└── Examples/
    └── LED_Blink/
```

## 设计原则

1. **分层解耦**: 各层之间通过接口交互，降低耦合度
2. **可扩展性**: 易于添加新的硬件模块和应用功能
3. **可重用性**: 组件可在不同项目间复用
4. **可测试性**: 支持单元测试和模块测试
5. **配置化**: 通过配置文件管理系统参数

## 快速开始

### 1. 基本使用

```c
#include "fw_framework.h"

int main(void)
{
    /* 硬件初始化 */
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_USART1_UART_Init();

    /* 初始化框架 */
    fw_framework_init(NULL);  // 使用默认配置

    /* 启动FreeRTOS */
    osKernelInitialize();
    MX_FREERTOS_Init();
    osKernelStart();
}
```

### 2. LED闪烁示例

```c
#include "fw_framework.h"
#include "led_blink_app.h"

void led_example(void)
{
    /* 创建LED应用 */
    led_app_context_t* led_app = led_app_create_default_instance();

    /* 启动LED任务 */
    led_app_start(led_app);

    /* 控制LED */
    led_app_start_slow_blink(led_app);  // 慢速闪烁
    led_app_start_fast_blink(led_app);  // 快速闪烁
    led_app_turn_on(led_app);           // 常亮
    led_app_turn_off(led_app);          // 关闭
}
```

### 3. 自定义GPIO控制

```c
#include "fw_gpio.h"

void gpio_example(void)
{
    /* 定义GPIO配置 */
    fw_gpio_config_t led_config = FW_GPIO_CONFIG_OUTPUT(GPIOA, GPIO_PIN_5, "LED");
    fw_gpio_handle_t led_handle = FW_GPIO_HANDLE_INIT(led_config, "LED");

    /* 初始化GPIO */
    fw_gpio_pin_init(&led_handle);

    /* 控制GPIO */
    fw_gpio_pin_write(&led_handle, FW_GPIO_PIN_SET);    // 输出高电平
    fw_gpio_pin_write(&led_handle, FW_GPIO_PIN_RESET);  // 输出低电平
    fw_gpio_pin_toggle(&led_handle);                    // 切换状态
}
```

### 4. 任务创建示例

```c
#include "fw_rtos.h"

void my_task_function(void* argument)
{
    while (1) {
        FW_LOG_INFO("TASK", "任务运行中...");
        fw_task_delay(1000);  // 延时1秒
    }
}

void task_example(void)
{
    /* 配置任务 */
    fw_task_config_t task_config = FW_TASK_CONFIG_INIT(
        "MyTask",                    // 任务名称
        my_task_function,            // 任务函数
        FW_TASK_PRIORITY_NORMAL,     // 任务优先级
        512,                         // 堆栈大小
        NULL                         // 任务参数
    );

    /* 创建任务句柄 */
    fw_task_handle_t task_handle = FW_TASK_HANDLE_INIT(task_config);

    /* 创建任务 */
    fw_task_create(&task_handle);
}
```

## 完整示例

参考以下文件了解完整的使用方法：
- `Examples/LED_Blink/` - LED闪烁应用示例
- `Examples/framework_usage_example.c` - 框架集成示例

## 配置选项

框架支持灵活的配置，可以根据项目需求启用或禁用特定功能：

```c
fw_framework_config_t config = {
    .enable_logger = true,           // 启用日志系统
    .log_level = FW_LOG_LEVEL_INFO, // 设置日志级别
    .enable_error_handler = true,    // 启用错误处理
    .enable_gpio = true,             // 启用GPIO抽象层
    .enable_rtos_wrapper = true,     // 启用RTOS封装
    .enable_app_framework = true     // 启用应用框架
};

fw_framework_init(&config);
```
