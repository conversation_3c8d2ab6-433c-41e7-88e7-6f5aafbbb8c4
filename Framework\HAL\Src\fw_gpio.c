/**
 ******************************************************************************
 * @file    fw_gpio.c
 * @brief   Framework GPIO abstraction layer implementation
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "fw_gpio.h"
#include "fw_logger.h"
#include "stm32g4xx_hal.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/

/**
 * @brief GPIO interrupt callback entry structure
 */
typedef struct
{
    uint16_t pin;
    fw_gpio_interrupt_callback_t callback;
    fw_gpio_handle_t *handle;
} fw_gpio_interrupt_entry_t;

/**
 * @brief GPIO context structure
 */
typedef struct
{
    bool initialized;
    fw_gpio_interrupt_entry_t interrupt_callbacks[FW_GPIO_MAX_INTERRUPT_CALLBACKS];
    uint32_t interrupt_count;
} fw_gpio_context_t;

/* Private define ------------------------------------------------------------*/
#define FW_GPIO_TAG "GPIO"

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/**
 * @brief Global GPIO context
 */
static fw_gpio_context_t g_gpio_ctx = {0};

/* Private function prototypes -----------------------------------------------*/
static uint32_t fw_gpio_convert_mode(fw_gpio_mode_t mode);
static uint32_t fw_gpio_convert_pull(fw_gpio_pull_t pull);
static uint32_t fw_gpio_convert_speed(fw_gpio_speed_t speed);
static fw_gpio_result_t fw_gpio_find_interrupt_entry(uint16_t pin, uint32_t *index);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief Initialize GPIO abstraction layer
 */
fw_gpio_result_t fw_gpio_init(void)
{
    if (g_gpio_ctx.initialized)
    {
        FW_LOG_WARN(FW_GPIO_TAG, "GPIO already initialized");
        return FW_GPIO_ALREADY_INITIALIZED;
    }

    /* Initialize context */
    memset(&g_gpio_ctx, 0, sizeof(g_gpio_ctx));
    g_gpio_ctx.initialized = true;

    FW_LOG_INFO(FW_GPIO_TAG, "GPIO abstraction layer initialized");

    return FW_GPIO_OK;
}

/**
 * @brief Deinitialize GPIO abstraction layer
 */
fw_gpio_result_t fw_gpio_deinit(void)
{
    if (!g_gpio_ctx.initialized)
    {
        return FW_GPIO_OK;
    }

    /* Clear interrupt callbacks */
    memset(g_gpio_ctx.interrupt_callbacks, 0, sizeof(g_gpio_ctx.interrupt_callbacks));
    g_gpio_ctx.interrupt_count = 0;
    g_gpio_ctx.initialized = false;

    FW_LOG_INFO(FW_GPIO_TAG, "GPIO abstraction layer deinitialized");

    return FW_GPIO_OK;
}

/**
 * @brief Initialize a GPIO pin
 */
fw_gpio_result_t fw_gpio_pin_init(fw_gpio_handle_t *handle)
{
    GPIO_InitTypeDef gpio_init = {0};

    /* Validate parameters */
    if (handle == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_GPIO_TAG, "Handle is NULL");
        return FW_GPIO_INVALID_PARAM;
    }

    if (!g_gpio_ctx.initialized)
    {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_GPIO_TAG, "GPIO not initialized");
        return FW_GPIO_NOT_INITIALIZED;
    }

    if (handle->initialized)
    {
        FW_LOG_WARN(FW_GPIO_TAG, "Pin %s already initialized", handle->name);
        return FW_GPIO_ALREADY_INITIALIZED;
    }

    /* Enable port clock */
    fw_gpio_result_t result = fw_gpio_enable_port_clock(handle->config.port);
    if (result != FW_GPIO_OK)
    {
        FW_ERROR_REPORT(FW_ERROR_HW_GPIO_INIT_FAILED, FW_GPIO_TAG,
                        "Failed to enable clock for pin %s", handle->name);
        return result;
    }

    /* Configure GPIO */
    gpio_init.Pin = handle->config.pin;
    gpio_init.Mode = fw_gpio_convert_mode(handle->config.mode);
    gpio_init.Pull = fw_gpio_convert_pull(handle->config.pull);
    gpio_init.Speed = fw_gpio_convert_speed(handle->config.speed);
    gpio_init.Alternate = handle->config.alternate;

    HAL_GPIO_Init(handle->config.port, &gpio_init);

    handle->initialized = true;

    FW_LOG_INFO(FW_GPIO_TAG, "Pin %s initialized (Port: %p, Pin: 0x%04X)",
                handle->name, handle->config.port, handle->config.pin);

    return FW_GPIO_OK;
}

/**
 * @brief Deinitialize a GPIO pin
 */
fw_gpio_result_t fw_gpio_pin_deinit(fw_gpio_handle_t *handle)
{
    /* Validate parameters */
    if (handle == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_GPIO_TAG, "Handle is NULL");
        return FW_GPIO_INVALID_PARAM;
    }

    if (!handle->initialized)
    {
        return FW_GPIO_OK;
    }

    /* Deinitialize GPIO */
    HAL_GPIO_DeInit(handle->config.port, handle->config.pin);

    handle->initialized = false;

    FW_LOG_INFO(FW_GPIO_TAG, "Pin %s deinitialized", handle->name);

    return FW_GPIO_OK;
}

/**
 * @brief Write to a GPIO pin
 */
fw_gpio_result_t fw_gpio_pin_write(fw_gpio_handle_t *handle, fw_gpio_pin_state_t state)
{
    /* Validate parameters */
    if (handle == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_GPIO_TAG, "Handle is NULL");
        return FW_GPIO_INVALID_PARAM;
    }

    if (!handle->initialized)
    {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_GPIO_TAG,
                        "Pin %s not initialized", handle->name);
        return FW_GPIO_NOT_INITIALIZED;
    }

    /* Write to GPIO */
    HAL_GPIO_WritePin(handle->config.port, handle->config.pin,
                      (state == FW_GPIO_PIN_SET) ? GPIO_PIN_SET : GPIO_PIN_RESET);

    FW_LOG_DEBUG(FW_GPIO_TAG, "Pin %s set to %s", handle->name,
                 (state == FW_GPIO_PIN_SET) ? "HIGH" : "LOW");

    return FW_GPIO_OK;
}

/**
 * @brief Read from a GPIO pin
 */
fw_gpio_result_t fw_gpio_pin_read(fw_gpio_handle_t *handle, fw_gpio_pin_state_t *state)
{
    GPIO_PinState pin_state;

    /* Validate parameters */
    if (handle == NULL || state == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_GPIO_TAG, "Handle or state is NULL");
        return FW_GPIO_INVALID_PARAM;
    }

    if (!handle->initialized)
    {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_GPIO_TAG,
                        "Pin %s not initialized", handle->name);
        return FW_GPIO_NOT_INITIALIZED;
    }

    /* Read from GPIO */
    pin_state = HAL_GPIO_ReadPin(handle->config.port, handle->config.pin);
    *state = (pin_state == GPIO_PIN_SET) ? FW_GPIO_PIN_SET : FW_GPIO_PIN_RESET;

    FW_LOG_DEBUG(FW_GPIO_TAG, "Pin %s read as %s", handle->name,
                 (*state == FW_GPIO_PIN_SET) ? "HIGH" : "LOW");

    return FW_GPIO_OK;
}

/**
 * @brief Toggle a GPIO pin
 */
fw_gpio_result_t fw_gpio_pin_toggle(fw_gpio_handle_t *handle)
{
    /* Validate parameters */
    if (handle == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_GPIO_TAG, "Handle is NULL");
        return FW_GPIO_INVALID_PARAM;
    }

    if (!handle->initialized)
    {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_GPIO_TAG,
                        "Pin %s not initialized", handle->name);
        return FW_GPIO_NOT_INITIALIZED;
    }

    /* Toggle GPIO */
    HAL_GPIO_TogglePin(handle->config.port, handle->config.pin);

    FW_LOG_DEBUG(FW_GPIO_TAG, "Pin %s toggled", handle->name);

    return FW_GPIO_OK;
}

/**
 * @brief Set GPIO pin interrupt callback
 */
fw_gpio_result_t fw_gpio_pin_set_interrupt_callback(fw_gpio_handle_t *handle,
                                                    fw_gpio_interrupt_callback_t callback)
{
    uint32_t index;

    /* Validate parameters */
    if (handle == NULL || callback == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_GPIO_TAG, "Handle or callback is NULL");
        return FW_GPIO_INVALID_PARAM;
    }

    if (!handle->initialized)
    {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_GPIO_TAG,
                        "Pin %s not initialized", handle->name);
        return FW_GPIO_NOT_INITIALIZED;
    }

    /* Check if callback already exists */
    if (fw_gpio_find_interrupt_entry(handle->config.pin, &index) == FW_GPIO_OK)
    {
        /* Update existing callback */
        g_gpio_ctx.interrupt_callbacks[index].callback = callback;
        g_gpio_ctx.interrupt_callbacks[index].handle = handle;
    }
    else
    {
        /* Add new callback */
        if (g_gpio_ctx.interrupt_count >= FW_GPIO_MAX_INTERRUPT_CALLBACKS)
        {
            FW_ERROR_REPORT(FW_ERROR_RESOURCE_NOT_AVAILABLE, FW_GPIO_TAG,
                            "Maximum interrupt callbacks reached");
            return FW_GPIO_ERROR;
        }

        g_gpio_ctx.interrupt_callbacks[g_gpio_ctx.interrupt_count].pin = handle->config.pin;
        g_gpio_ctx.interrupt_callbacks[g_gpio_ctx.interrupt_count].callback = callback;
        g_gpio_ctx.interrupt_callbacks[g_gpio_ctx.interrupt_count].handle = handle;
        g_gpio_ctx.interrupt_count++;
    }

    FW_LOG_INFO(FW_GPIO_TAG, "Interrupt callback set for pin %s", handle->name);

    return FW_GPIO_OK;
}

/**
 * @brief Enable GPIO pin interrupt
 */
fw_gpio_result_t fw_gpio_pin_enable_interrupt(fw_gpio_handle_t *handle)
{
    /* Validate parameters */
    if (handle == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_GPIO_TAG, "Handle is NULL");
        return FW_GPIO_INVALID_PARAM;
    }

    if (!handle->initialized)
    {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_GPIO_TAG,
                        "Pin %s not initialized", handle->name);
        return FW_GPIO_NOT_INITIALIZED;
    }

    /* Enable interrupt in NVIC */
    IRQn_Type irq_number;

    /* Determine EXTI IRQ number based on pin */
    if (handle->config.pin == GPIO_PIN_0)
    {
        irq_number = EXTI0_IRQn;
    }
    else if (handle->config.pin == GPIO_PIN_1)
    {
        irq_number = EXTI1_IRQn;
    }
    else if (handle->config.pin == GPIO_PIN_2)
    {
        irq_number = EXTI2_IRQn;
    }
    else if (handle->config.pin == GPIO_PIN_3)
    {
        irq_number = EXTI3_IRQn;
    }
    else if (handle->config.pin == GPIO_PIN_4)
    {
        irq_number = EXTI4_IRQn;
    }
    else if (handle->config.pin >= GPIO_PIN_5 && handle->config.pin <= GPIO_PIN_9)
    {
        irq_number = EXTI9_5_IRQn;
    }
    else if (handle->config.pin >= GPIO_PIN_10 && handle->config.pin <= GPIO_PIN_15)
    {
        irq_number = EXTI15_10_IRQn;
    }
    else
    {
        FW_ERROR_REPORT(FW_ERROR_INVALID_PARAMETER, FW_GPIO_TAG,
                        "Invalid pin for interrupt: 0x%04X", handle->config.pin);
        return FW_GPIO_INVALID_PARAM;
    }

    HAL_NVIC_SetPriority(irq_number, 5, 0);
    HAL_NVIC_EnableIRQ(irq_number);

    FW_LOG_INFO(FW_GPIO_TAG, "Interrupt enabled for pin %s", handle->name);

    return FW_GPIO_OK;
}

/**
 * @brief Disable GPIO pin interrupt
 */
fw_gpio_result_t fw_gpio_pin_disable_interrupt(fw_gpio_handle_t *handle)
{
    /* Validate parameters */
    if (handle == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_GPIO_TAG, "Handle is NULL");
        return FW_GPIO_INVALID_PARAM;
    }

    if (!handle->initialized)
    {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_GPIO_TAG,
                        "Pin %s not initialized", handle->name);
        return FW_GPIO_NOT_INITIALIZED;
    }

    /* Note: Disabling individual pin interrupts requires careful handling */
    /* as multiple pins may share the same EXTI line */

    FW_LOG_INFO(FW_GPIO_TAG, "Interrupt disabled for pin %s", handle->name);

    return FW_GPIO_OK;
}

/**
 * @brief GPIO interrupt handler
 */
void fw_gpio_interrupt_handler(uint16_t pin)
{
    uint32_t index;

    /* Find and call interrupt callback */
    if (fw_gpio_find_interrupt_entry(pin, &index) == FW_GPIO_OK)
    {
        if (g_gpio_ctx.interrupt_callbacks[index].callback != NULL)
        {
            g_gpio_ctx.interrupt_callbacks[index].callback(g_gpio_ctx.interrupt_callbacks[index].handle);
        }
    }
}

/**
 * @brief Get GPIO pin name
 */
const char *fw_gpio_pin_get_name(fw_gpio_handle_t *handle)
{
    if (handle == NULL)
    {
        return "UNKNOWN";
    }

    return handle->name ? handle->name : "UNNAMED";
}

/**
 * @brief Check if GPIO pin is initialized
 */
bool fw_gpio_pin_is_initialized(fw_gpio_handle_t *handle)
{
    if (handle == NULL)
    {
        return false;
    }

    return handle->initialized;
}

/**
 * @brief Enable GPIO port clock
 */
fw_gpio_result_t fw_gpio_enable_port_clock(GPIO_TypeDef *port)
{
    if (port == GPIOA)
    {
        __HAL_RCC_GPIOA_CLK_ENABLE();
    }
    else if (port == GPIOB)
    {
        __HAL_RCC_GPIOB_CLK_ENABLE();
    }
    else if (port == GPIOC)
    {
        __HAL_RCC_GPIOC_CLK_ENABLE();
    }
    else if (port == GPIOD)
    {
        __HAL_RCC_GPIOD_CLK_ENABLE();
    }
    else if (port == GPIOE)
    {
        __HAL_RCC_GPIOE_CLK_ENABLE();
    }
    else if (port == GPIOF)
    {
        __HAL_RCC_GPIOF_CLK_ENABLE();
    }
    else if (port == GPIOG)
    {
        __HAL_RCC_GPIOG_CLK_ENABLE();
    }
    else
    {
        FW_ERROR_REPORT(FW_ERROR_INVALID_PARAMETER, FW_GPIO_TAG, "Invalid GPIO port");
        return FW_GPIO_INVALID_PARAM;
    }

    return FW_GPIO_OK;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Convert framework GPIO mode to HAL mode
 */
static uint32_t fw_gpio_convert_mode(fw_gpio_mode_t mode)
{
    switch (mode)
    {
    case FW_GPIO_MODE_INPUT:
        return GPIO_MODE_INPUT;
    case FW_GPIO_MODE_OUTPUT_PP:
        return GPIO_MODE_OUTPUT_PP;
    case FW_GPIO_MODE_OUTPUT_OD:
        return GPIO_MODE_OUTPUT_OD;
    case FW_GPIO_MODE_AF_PP:
        return GPIO_MODE_AF_PP;
    case FW_GPIO_MODE_AF_OD:
        return GPIO_MODE_AF_OD;
    case FW_GPIO_MODE_ANALOG:
        return GPIO_MODE_ANALOG;
    case FW_GPIO_MODE_IT_RISING:
        return GPIO_MODE_IT_RISING;
    case FW_GPIO_MODE_IT_FALLING:
        return GPIO_MODE_IT_FALLING;
    case FW_GPIO_MODE_IT_RISING_FALLING:
        return GPIO_MODE_IT_RISING_FALLING;
    case FW_GPIO_MODE_EVT_RISING:
        return GPIO_MODE_EVT_RISING;
    case FW_GPIO_MODE_EVT_FALLING:
        return GPIO_MODE_EVT_FALLING;
    case FW_GPIO_MODE_EVT_RISING_FALLING:
        return GPIO_MODE_EVT_RISING_FALLING;
    default:
        return GPIO_MODE_INPUT;
    }
}

/**
 * @brief Convert framework GPIO pull to HAL pull
 */
static uint32_t fw_gpio_convert_pull(fw_gpio_pull_t pull)
{
    switch (pull)
    {
    case FW_GPIO_NOPULL:
        return GPIO_NOPULL;
    case FW_GPIO_PULLUP:
        return GPIO_PULLUP;
    case FW_GPIO_PULLDOWN:
        return GPIO_PULLDOWN;
    default:
        return GPIO_NOPULL;
    }
}

/**
 * @brief Convert framework GPIO speed to HAL speed
 */
static uint32_t fw_gpio_convert_speed(fw_gpio_speed_t speed)
{
    switch (speed)
    {
    case FW_GPIO_SPEED_LOW:
        return GPIO_SPEED_FREQ_LOW;
    case FW_GPIO_SPEED_MEDIUM:
        return GPIO_SPEED_FREQ_MEDIUM;
    case FW_GPIO_SPEED_HIGH:
        return GPIO_SPEED_FREQ_HIGH;
    case FW_GPIO_SPEED_VERY_HIGH:
        return GPIO_SPEED_FREQ_VERY_HIGH;
    default:
        return GPIO_SPEED_FREQ_LOW;
    }
}

/**
 * @brief Find interrupt callback entry by pin
 */
static fw_gpio_result_t fw_gpio_find_interrupt_entry(uint16_t pin, uint32_t *index)
{
    for (uint32_t i = 0; i < g_gpio_ctx.interrupt_count; i++)
    {
        if (g_gpio_ctx.interrupt_callbacks[i].pin == pin)
        {
            *index = i;
            return FW_GPIO_OK;
        }
    }

    return FW_GPIO_ERROR;
}
