/**
  ******************************************************************************
  * @file    fw_app.h
  * @brief   框架应用层头文件
  ******************************************************************************
  * @attention
  *
  * 此文件包含STM32G474嵌入式开发框架的应用层定义和函数。
  *
  ******************************************************************************
  */

#ifndef __FW_APP_H__
#define __FW_APP_H__

#ifdef __cplusplus
extern "C" {
#endif

/* 包含文件 ------------------------------------------------------------------*/
#include "fw_config.h"
#include "fw_error.h"
#include "fw_rtos.h"
#include <stdint.h>
#include <stdbool.h>

/* 导出类型 ------------------------------------------------------------------*/

/**
 * @brief 应用状态枚举
 */
typedef enum {
    FW_APP_STATE_INIT = 0,
    FW_APP_STATE_IDLE,
    FW_APP_STATE_RUNNING,
    FW_APP_STATE_SUSPENDED,
    FW_APP_STATE_ERROR,
    FW_APP_STATE_SHUTDOWN
} fw_app_state_t;

/**
 * @brief 事件类型枚举
 */
typedef enum {
    FW_EVENT_TYPE_NONE = 0,
    FW_EVENT_TYPE_SYSTEM,
    FW_EVENT_TYPE_USER,
    FW_EVENT_TYPE_TIMER,
    FW_EVENT_TYPE_GPIO,
    FW_EVENT_TYPE_COMM,
    FW_EVENT_TYPE_ERROR
} fw_event_type_t;

/**
 * @brief 事件结构体
 */
typedef struct {
    fw_event_type_t type;
    uint32_t id;
    uint32_t timestamp;
    void* data;
    uint32_t data_size;
} fw_event_t;

/**
 * @brief 事件处理函数类型
 */
typedef fw_rtos_result_t (*fw_event_handler_t)(const fw_event_t* event);

/**
 * @brief 状态机状态结构体
 */
typedef struct {
    uint32_t state_id;
    const char* name;
    fw_event_handler_t entry_handler;
    fw_event_handler_t exit_handler;
    fw_event_handler_t event_handler;
} fw_state_t;

/**
 * @brief 状态机转换结构体
 */
typedef struct {
    uint32_t from_state;
    uint32_t to_state;
    fw_event_type_t trigger_event;
    uint32_t event_id;
    fw_event_handler_t guard_condition;
} fw_transition_t;

/**
 * @brief 状态机句柄结构体
 */
typedef struct {
    const char* name;
    const fw_state_t* states;
    uint32_t state_count;
    const fw_transition_t* transitions;
    uint32_t transition_count;
    uint32_t current_state;
    bool initialized;
} fw_state_machine_t;

/**
 * @brief 应用任务基类结构体
 */
typedef struct {
    fw_task_handle_t task_handle;
    fw_queue_handle_t event_queue;
    fw_state_machine_t* state_machine;
    fw_app_state_t app_state;
    const char* name;
    bool initialized;
} fw_app_task_t;

/**
 * @brief 应用结果枚举
 */
typedef enum {
    FW_APP_OK = 0,
    FW_APP_ERROR,
    FW_APP_INVALID_PARAM,
    FW_APP_NOT_INITIALIZED,
    FW_APP_ALREADY_INITIALIZED,
    FW_APP_STATE_MACHINE_ERROR,
    FW_APP_EVENT_QUEUE_FULL
} fw_app_result_t;

/* 导出常量 ------------------------------------------------------------------*/

/* 事件队列默认大小 */
#define FW_APP_EVENT_QUEUE_SIZE         16

/* 最大状态数量 */
#define FW_APP_MAX_STATES               32

/* 最大转换数量 */
#define FW_APP_MAX_TRANSITIONS          64

/* 事件超时时间 */
#define FW_APP_EVENT_TIMEOUT            1000

/* 导出宏 --------------------------------------------------------------------*/

/**
 * @brief 事件初始化宏
 */
#define FW_EVENT_INIT(event_type, event_id, event_data, event_data_size) \
    { .type = event_type, .id = event_id, .timestamp = fw_rtos_get_tick_count(), \
      .data = event_data, .data_size = event_data_size }

/**
 * @brief 状态定义宏
 */
#define FW_STATE_DEFINE(state_id, state_name, entry_func, exit_func, event_func) \
    { .state_id = state_id, .name = state_name, .entry_handler = entry_func, \
      .exit_handler = exit_func, .event_handler = event_func }

/**
 * @brief 转换定义宏
 */
#define FW_TRANSITION_DEFINE(from, to, trigger_type, trigger_id, guard_func) \
    { .from_state = from, .to_state = to, .trigger_event = trigger_type, \
      .event_id = trigger_id, .guard_condition = guard_func }

/* 导出函数原型 --------------------------------------------------------------*/

/**
 * @brief 初始化应用层框架
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_init(void);

/**
 * @brief 反初始化应用层框架
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_deinit(void);

/**
 * @brief 创建应用任务
 * @param app_task 应用任务指针
 * @param task_config 任务配置
 * @param state_machine 状态机指针
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_task_create(fw_app_task_t* app_task,
                                  const fw_task_config_t* task_config,
                                  fw_state_machine_t* state_machine);

/**
 * @brief 删除应用任务
 * @param app_task 应用任务指针
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_task_delete(fw_app_task_t* app_task);

/**
 * @brief 发送事件到应用任务
 * @param app_task 应用任务指针
 * @param event 事件指针
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_task_send_event(fw_app_task_t* app_task, const fw_event_t* event);

/**
 * @brief 应用任务主循环（在任务函数中调用）
 * @param app_task 应用任务指针
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_task_run(fw_app_task_t* app_task);

/**
 * @brief 初始化状态机
 * @param sm 状态机指针
 * @param name 状态机名称
 * @param states 状态数组
 * @param state_count 状态数量
 * @param transitions 转换数组
 * @param transition_count 转换数量
 * @param initial_state 初始状态ID
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_state_machine_init(fw_state_machine_t* sm,
                                     const char* name,
                                     const fw_state_t* states,
                                     uint32_t state_count,
                                     const fw_transition_t* transitions,
                                     uint32_t transition_count,
                                     uint32_t initial_state);

/**
 * @brief 处理状态机事件
 * @param sm 状态机指针
 * @param event 事件指针
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_state_machine_process_event(fw_state_machine_t* sm, const fw_event_t* event);

/**
 * @brief 获取当前状态
 * @param sm 状态机指针
 * @retval uint32_t 当前状态ID
 */
uint32_t fw_state_machine_get_current_state(const fw_state_machine_t* sm);

/**
 * @brief 强制状态转换
 * @param sm 状态机指针
 * @param new_state 新状态ID
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_state_machine_force_transition(fw_state_machine_t* sm, uint32_t new_state);

/**
 * @brief 广播事件到所有应用任务
 * @param event 事件指针
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_broadcast_event(const fw_event_t* event);

/**
 * @brief 注册应用任务到框架
 * @param app_task 应用任务指针
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_register_task(fw_app_task_t* app_task);

/**
 * @brief 注销应用任务
 * @param app_task 应用任务指针
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_unregister_task(fw_app_task_t* app_task);

/**
 * @brief 获取应用任务状态
 * @param app_task 应用任务指针
 * @retval fw_app_state_t 应用状态
 */
fw_app_state_t fw_app_task_get_state(const fw_app_task_t* app_task);

/**
 * @brief 设置应用任务状态
 * @param app_task 应用任务指针
 * @param state 新状态
 * @retval fw_app_result_t 应用结果
 */
fw_app_result_t fw_app_task_set_state(fw_app_task_t* app_task, fw_app_state_t state);

/**
 * @brief 创建系统事件
 * @param event_id 事件ID
 * @param data 事件数据
 * @param data_size 数据大小
 * @retval fw_event_t 创建的事件
 */
fw_event_t fw_app_create_system_event(uint32_t event_id, void* data, uint32_t data_size);

/**
 * @brief 创建用户事件
 * @param event_id 事件ID
 * @param data 事件数据
 * @param data_size 数据大小
 * @retval fw_event_t 创建的事件
 */
fw_event_t fw_app_create_user_event(uint32_t event_id, void* data, uint32_t data_size);

#ifdef __cplusplus
}
#endif

#endif /* __FW_APP_H__ */
