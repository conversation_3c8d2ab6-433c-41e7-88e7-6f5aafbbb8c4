/**
  ******************************************************************************
  * @file    fw_framework.h
  * @brief   框架主头文件
  ******************************************************************************
  * @attention
  *
  * 此文件包含STM32G474嵌入式开发框架的主要包含文件和初始化函数。
  *
  ******************************************************************************
  */

#ifndef __FW_FRAMEWORK_H__
#define __FW_FRAMEWORK_H__

#ifdef __cplusplus
extern "C" {
#endif

/* 包含文件 ------------------------------------------------------------------*/
/* 公共资源层 */
#include "fw_config.h"
#include "fw_logger.h"
#include "fw_error.h"

/* 硬件抽象层 */
#include "fw_gpio.h"

/* 中间件层 */
#include "fw_rtos.h"

/* 应用层 */
#include "fw_app.h"

/* 标准库 */
#include <stdint.h>
#include <stdbool.h>

/* 导出类型 ------------------------------------------------------------------*/

/**
 * @brief 框架初始化结果枚举
 */
typedef enum {
    FW_FRAMEWORK_OK = 0,
    FW_FRAMEWORK_ERROR,
    FW_FRAMEWORK_CONFIG_ERROR,
    FW_FRAMEWORK_LOGGER_ERROR,
    FW_FRAMEWORK_ERROR_HANDLER_ERROR,
    FW_FRAMEWORK_GPIO_ERROR,
    FW_FRAMEWORK_RTOS_ERROR,
    FW_FRAMEWORK_APP_ERROR,
    FW_FRAMEWORK_ALREADY_INITIALIZED
} fw_framework_result_t;

/**
 * @brief 框架配置结构体
 */
typedef struct {
    /* 日志配置 */
    bool enable_logger;
    fw_log_level_t log_level;
    fw_log_output_callback_t log_output_callback;
    
    /* 错误处理配置 */
    bool enable_error_handler;
    fw_error_handler_callback_t error_callback;
    
    /* 硬件模块配置 */
    bool enable_gpio;
    bool enable_uart;
    bool enable_timer;
    bool enable_adc;
    
    /* RTOS配置 */
    bool enable_rtos_wrapper;
    
    /* 应用层配置 */
    bool enable_app_framework;
} fw_framework_config_t;

/* 导出常量 ------------------------------------------------------------------*/

/* 框架版本信息 */
#define FW_FRAMEWORK_VERSION_MAJOR      1
#define FW_FRAMEWORK_VERSION_MINOR      0
#define FW_FRAMEWORK_VERSION_PATCH      0
#define FW_FRAMEWORK_VERSION_STRING     "1.0.0"

/* 导出宏 --------------------------------------------------------------------*/

/**
 * @brief 默认框架配置宏
 */
#define FW_FRAMEWORK_CONFIG_DEFAULT() \
    { \
        .enable_logger = true, \
        .log_level = FW_LOG_LEVEL_INFO, \
        .log_output_callback = fw_logger_uart_output, \
        .enable_error_handler = true, \
        .error_callback = fw_error_default_handler, \
        .enable_gpio = true, \
        .enable_uart = true, \
        .enable_timer = false, \
        .enable_adc = false, \
        .enable_rtos_wrapper = true, \
        .enable_app_framework = true \
    }

/**
 * @brief 最小框架配置宏（仅基本功能）
 */
#define FW_FRAMEWORK_CONFIG_MINIMAL() \
    { \
        .enable_logger = false, \
        .log_level = FW_LOG_LEVEL_ERROR, \
        .log_output_callback = NULL, \
        .enable_error_handler = false, \
        .error_callback = NULL, \
        .enable_gpio = true, \
        .enable_uart = false, \
        .enable_timer = false, \
        .enable_adc = false, \
        .enable_rtos_wrapper = true, \
        .enable_app_framework = false \
    }

/* 导出函数原型 --------------------------------------------------------------*/

/**
 * @brief 初始化框架
 * @param config 框架配置指针（NULL使用默认配置）
 * @retval fw_framework_result_t 框架初始化结果
 */
fw_framework_result_t fw_framework_init(const fw_framework_config_t* config);

/**
 * @brief 反初始化框架
 * @retval fw_framework_result_t 框架反初始化结果
 */
fw_framework_result_t fw_framework_deinit(void);

/**
 * @brief 检查框架是否已初始化
 * @retval bool 初始化状态
 */
bool fw_framework_is_initialized(void);

/**
 * @brief 获取框架版本信息
 * @retval const char* 版本字符串
 */
const char* fw_framework_get_version(void);

/**
 * @brief 获取框架配置信息
 * @retval const fw_framework_config_t* 当前配置指针
 */
const fw_framework_config_t* fw_framework_get_config(void);

/**
 * @brief 框架运行时检查
 * @retval fw_framework_result_t 检查结果
 */
fw_framework_result_t fw_framework_runtime_check(void);

/**
 * @brief 获取框架状态信息
 * @param info_buffer 信息缓冲区
 * @param buffer_size 缓冲区大小
 * @retval fw_framework_result_t 获取结果
 */
fw_framework_result_t fw_framework_get_status_info(char* info_buffer, uint32_t buffer_size);

/**
 * @brief 框架软件复位
 * @retval fw_framework_result_t 复位结果
 */
fw_framework_result_t fw_framework_soft_reset(void);

/**
 * @brief 设置框架日志级别
 * @param level 新的日志级别
 * @retval fw_framework_result_t 设置结果
 */
fw_framework_result_t fw_framework_set_log_level(fw_log_level_t level);

/**
 * @brief 启用/禁用框架模块
 * @param module_name 模块名称
 * @param enabled 启用状态
 * @retval fw_framework_result_t 设置结果
 */
fw_framework_result_t fw_framework_set_module_enabled(const char* module_name, bool enabled);

/**
 * @brief 获取框架内存使用情况
 * @param heap_free 空闲堆内存指针
 * @param heap_total 总堆内存指针
 * @retval fw_framework_result_t 获取结果
 */
fw_framework_result_t fw_framework_get_memory_info(uint32_t* heap_free, uint32_t* heap_total);

/**
 * @brief 框架性能统计
 * @param cpu_usage CPU使用率指针
 * @param task_count 任务数量指针
 * @retval fw_framework_result_t 获取结果
 */
fw_framework_result_t fw_framework_get_performance_info(uint32_t* cpu_usage, uint32_t* task_count);

#ifdef __cplusplus
}
#endif

#endif /* __FW_FRAMEWORK_H__ */
