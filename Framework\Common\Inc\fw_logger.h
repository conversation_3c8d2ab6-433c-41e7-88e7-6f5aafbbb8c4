/**
 ******************************************************************************
 * @file    fw_logger.h
 * @brief   框架日志系统头文件
 ******************************************************************************
 * @attention
 *
 * 此文件包含STM32G474嵌入式开发框架的日志系统定义和函数。
 *
 ******************************************************************************
 */

#ifndef __FW_LOGGER_H__
#define __FW_LOGGER_H__

#ifdef __cplusplus
extern "C"
{
#endif

/* 包含文件 ------------------------------------------------------------------*/
#include "fw_config.h"
#include <stdint.h>
#include <stdarg.h>

    /* 导出类型 ------------------------------------------------------------------*/

    /**
     * @brief 日志消息结构体
     */
    typedef struct
    {
        uint32_t timestamp;
        fw_log_level_t level;
        const char *tag;
        char message[128];
    } fw_log_message_t;

    /**
     * @brief 日志器结果枚举
     */
    typedef enum
    {
        FW_LOGGER_OK = 0,
        FW_LOGGER_ERROR,
        FW_LOGGER_BUFFER_FULL,
        FW_LOGGER_INVALID_PARAM
    } fw_logger_result_t;

    /**
     * @brief 日志输出回调函数类型
     */
    typedef void (*fw_log_output_callback_t)(const fw_log_message_t *message);

/* 导出常量 ------------------------------------------------------------------*/

/* 缓冲区中日志消息的最大数量 */
#define FW_LOG_BUFFER_SIZE 32

/* 标签最大长度 */
#define FW_LOG_TAG_MAX_LENGTH 16

/* 日志级别字符串 */
#define FW_LOG_LEVEL_ERROR_STR "ERROR"
#define FW_LOG_LEVEL_WARN_STR "WARN "
#define FW_LOG_LEVEL_INFO_STR "INFO "
#define FW_LOG_LEVEL_DEBUG_STR "DEBUG"

/* Exported macro ------------------------------------------------------------*/

/**
 * @brief Log macros for different levels
 */
#define FW_LOG_ERROR(tag, format, ...) fw_log(FW_LOG_LEVEL_ERROR, tag, format, ##__VA_ARGS__)
#define FW_LOG_WARN(tag, format, ...) fw_log(FW_LOG_LEVEL_WARN, tag, format, ##__VA_ARGS__)
#define FW_LOG_INFO(tag, format, ...) fw_log(FW_LOG_LEVEL_INFO, tag, format, ##__VA_ARGS__)
#define FW_LOG_DEBUG(tag, format, ...) fw_log(FW_LOG_LEVEL_DEBUG, tag, format, ##__VA_ARGS__)

/**
 * @brief Conditional logging macros (only log if level is enabled)
 */
#define FW_LOG_ERROR_IF(condition, tag, format, ...)  \
    do                                                \
    {                                                 \
        if (condition)                                \
            FW_LOG_ERROR(tag, format, ##__VA_ARGS__); \
    } while (0)

#define FW_LOG_WARN_IF(condition, tag, format, ...)  \
    do                                               \
    {                                                \
        if (condition)                               \
            FW_LOG_WARN(tag, format, ##__VA_ARGS__); \
    } while (0)

#define FW_LOG_INFO_IF(condition, tag, format, ...)  \
    do                                               \
    {                                                \
        if (condition)                               \
            FW_LOG_INFO(tag, format, ##__VA_ARGS__); \
    } while (0)

#define FW_LOG_DEBUG_IF(condition, tag, format, ...)  \
    do                                                \
    {                                                 \
        if (condition)                                \
            FW_LOG_DEBUG(tag, format, ##__VA_ARGS__); \
    } while (0)

/**
 * @brief Function entry/exit logging macros
 */
#define FW_LOG_FUNC_ENTRY(tag) FW_LOG_DEBUG(tag, ">>> %s", __FUNCTION__)
#define FW_LOG_FUNC_EXIT(tag) FW_LOG_DEBUG(tag, "<<< %s", __FUNCTION__)

    /* Exported functions prototypes ---------------------------------------------*/

    /**
     * @brief Initialize the logging system
     * @retval fw_logger_result_t Logger result
     */
    fw_logger_result_t fw_logger_init(void);

    /**
     * @brief Deinitialize the logging system
     * @retval fw_logger_result_t Logger result
     */
    fw_logger_result_t fw_logger_deinit(void);

    /**
     * @brief Log a message with specified level and tag
     * @param level Log level
     * @param tag Log tag (module name)
     * @param format Printf-style format string
     * @param ... Variable arguments
     * @retval fw_logger_result_t Logger result
     */
    fw_logger_result_t fw_log(fw_log_level_t level, const char *tag, const char *format, ...);

    /**
     * @brief Log a message with variable arguments list
     * @param level Log level
     * @param tag Log tag (module name)
     * @param format Printf-style format string
     * @param args Variable arguments list
     * @retval fw_logger_result_t Logger result
     */
    fw_logger_result_t fw_log_va(fw_log_level_t level, const char *tag, const char *format, va_list args);

    /**
     * @brief Set log output callback function
     * @param callback Output callback function
     * @retval fw_logger_result_t Logger result
     */
    fw_logger_result_t fw_logger_set_output_callback(fw_log_output_callback_t callback);

    /**
     * @brief Enable/disable logging
     * @param enabled Logging state
     * @retval fw_logger_result_t Logger result
     */
    fw_logger_result_t fw_logger_set_enabled(bool enabled);

    /**
     * @brief Set minimum log level
     * @param level Minimum log level
     * @retval fw_logger_result_t Logger result
     */
    fw_logger_result_t fw_logger_set_level(fw_log_level_t level);

    /**
     * @brief Get current log level
     * @retval fw_log_level_t Current log level
     */
    fw_log_level_t fw_logger_get_level(void);

    /**
     * @brief Check if logging is enabled for specified level
     * @param level Log level to check
     * @retval bool True if logging is enabled for this level
     */
    bool fw_logger_is_level_enabled(fw_log_level_t level);

    /**
     * @brief Flush log buffer (output all pending messages)
     * @retval fw_logger_result_t Logger result
     */
    fw_logger_result_t fw_logger_flush(void);

    /**
     * @brief Get number of messages in log buffer
     * @retval uint32_t Number of pending messages
     */
    uint32_t fw_logger_get_pending_count(void);

    /**
     * @brief Clear log buffer
     * @retval fw_logger_result_t Logger result
     */
    fw_logger_result_t fw_logger_clear_buffer(void);

    /**
     * @brief Get log level string representation
     * @param level Log level
     * @retval const char* Log level string
     */
    const char *fw_logger_get_level_string(fw_log_level_t level);

    /**
     * @brief Default UART output callback (can be used as output callback)
     * @param message Log message to output
     */
    void fw_logger_uart_output(const fw_log_message_t *message);

#ifdef __cplusplus
}
#endif

#endif /* __FW_LOGGER_H__ */
