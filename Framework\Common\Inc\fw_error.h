/**
 ******************************************************************************
 * @file    fw_error.h
 * @brief   框架错误处理系统头文件
 ******************************************************************************
 * @attention
 *
 * 此文件包含STM32G474嵌入式开发框架的错误处理系统定义和函数。
 *
 ******************************************************************************
 */

#ifndef __FW_ERROR_H__
#define __FW_ERROR_H__

#ifdef __cplusplus
extern "C"
{
#endif

/* 包含文件 ------------------------------------------------------------------*/
#include "fw_config.h"
#include <stdint.h>
#include <stdbool.h>

    /* 导出类型 ------------------------------------------------------------------*/

    /**
     * @brief 框架错误代码枚举
     */
    typedef enum
    {
        FW_ERROR_NONE = 0,

        /* System errors */
        FW_ERROR_SYSTEM_INIT_FAILED,
        FW_ERROR_SYSTEM_CONFIG_INVALID,
        FW_ERROR_SYSTEM_MEMORY_ALLOCATION_FAILED,
        FW_ERROR_SYSTEM_TASK_CREATION_FAILED,

        /* Hardware errors */
        FW_ERROR_HW_GPIO_INIT_FAILED,
        FW_ERROR_HW_UART_INIT_FAILED,
        FW_ERROR_HW_TIMER_INIT_FAILED,
        FW_ERROR_HW_ADC_INIT_FAILED,
        FW_ERROR_HW_PERIPHERAL_NOT_READY,

        /* Communication errors */
        FW_ERROR_COMM_TIMEOUT,
        FW_ERROR_COMM_BUFFER_OVERFLOW,
        FW_ERROR_COMM_INVALID_DATA,
        FW_ERROR_COMM_CONNECTION_LOST,

        /* Application errors */
        FW_ERROR_APP_INVALID_PARAMETER,
        FW_ERROR_APP_INVALID_STATE,
        FW_ERROR_APP_OPERATION_FAILED,
        FW_ERROR_APP_RESOURCE_BUSY,

        /* Framework errors */
        FW_ERROR_FW_NOT_INITIALIZED,
        FW_ERROR_FW_ALREADY_INITIALIZED,
        FW_ERROR_FW_INVALID_CONFIGURATION,
        FW_ERROR_FW_OPERATION_NOT_SUPPORTED,

        /* Generic errors */
        FW_ERROR_INVALID_PARAMETER,
        FW_ERROR_NULL_POINTER,
        FW_ERROR_BUFFER_TOO_SMALL,
        FW_ERROR_OPERATION_TIMEOUT,
        FW_ERROR_RESOURCE_NOT_AVAILABLE,

        FW_ERROR_COUNT /* Must be last */
    } fw_error_code_t;

    /**
     * @brief Error severity levels
     */
    typedef enum
    {
        FW_ERROR_SEVERITY_INFO = 0,
        FW_ERROR_SEVERITY_WARNING,
        FW_ERROR_SEVERITY_ERROR,
        FW_ERROR_SEVERITY_CRITICAL
    } fw_error_severity_t;

    /**
     * @brief Error information structure
     */
    typedef struct
    {
        fw_error_code_t code;
        fw_error_severity_t severity;
        uint32_t timestamp;
        const char *file;
        uint32_t line;
        const char *function;
        const char *module;
        char description[128];
    } fw_error_info_t;

    /**
     * @brief Error handler callback function type
     */
    typedef void (*fw_error_handler_callback_t)(const fw_error_info_t *error_info);

    /**
     * @brief Error handler result enumeration
     */
    typedef enum
    {
        FW_ERROR_HANDLER_OK = 0,
        FW_ERROR_HANDLER_ERROR,
        FW_ERROR_HANDLER_BUFFER_FULL,
        FW_ERROR_HANDLER_INVALID_PARAM
    } fw_error_handler_result_t;

/* Exported constants --------------------------------------------------------*/

/* Maximum number of error records in history */
#define FW_ERROR_HISTORY_SIZE 16

/* Maximum module name length */
#define FW_ERROR_MODULE_NAME_MAX_LENGTH 16

/* Exported macro ------------------------------------------------------------*/

/**
 * @brief Error reporting macros
 */
#define FW_ERROR_REPORT(code, module, format, ...) \
    fw_error_report(code, FW_ERROR_SEVERITY_ERROR, module, __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__)

#define FW_ERROR_REPORT_CRITICAL(code, module, format, ...) \
    fw_error_report(code, FW_ERROR_SEVERITY_CRITICAL, module, __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__)

#define FW_ERROR_REPORT_WARNING(code, module, format, ...) \
    fw_error_report(code, FW_ERROR_SEVERITY_WARNING, module, __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__)

#define FW_ERROR_REPORT_INFO(code, module, format, ...) \
    fw_error_report(code, FW_ERROR_SEVERITY_INFO, module, __FILE__, __LINE__, __FUNCTION__, format, ##__VA_ARGS__)

/**
 * @brief Error checking macros
 */
#define FW_ERROR_CHECK(condition, code, module, format, ...)      \
    do                                                            \
    {                                                             \
        if (!(condition))                                         \
        {                                                         \
            FW_ERROR_REPORT(code, module, format, ##__VA_ARGS__); \
            return code;                                          \
        }                                                         \
    } while (0)

#define FW_ERROR_CHECK_RETURN_VOID(condition, code, module, format, ...) \
    do                                                                   \
    {                                                                    \
        if (!(condition))                                                \
        {                                                                \
            FW_ERROR_REPORT(code, module, format, ##__VA_ARGS__);        \
            return;                                                      \
        }                                                                \
    } while (0)

/**
 * @brief Assert macros (only in debug mode)
 */
#ifdef DEBUG
#define FW_ASSERT(condition, module, format, ...)                                                           \
    do                                                                                                      \
    {                                                                                                       \
        if (!(condition))                                                                                   \
        {                                                                                                   \
            FW_ERROR_REPORT_CRITICAL(FW_ERROR_APP_INVALID_STATE, module, "ASSERT: " format, ##__VA_ARGS__); \
            while (1)                                                                                       \
                ;                                                                                           \
        }                                                                                                   \
    } while (0)
#else
#define FW_ASSERT(condition, module, format, ...) ((void)0)
#endif

    /* Exported functions prototypes ---------------------------------------------*/

    /**
     * @brief Initialize error handling system
     * @retval fw_error_handler_result_t Error handler result
     */
    fw_error_handler_result_t fw_error_handler_init(void);

    /**
     * @brief Deinitialize error handling system
     * @retval fw_error_handler_result_t Error handler result
     */
    fw_error_handler_result_t fw_error_handler_deinit(void);

    /**
     * @brief Report an error
     * @param code Error code
     * @param severity Error severity
     * @param module Module name
     * @param file Source file name
     * @param line Line number
     * @param function Function name
     * @param format Printf-style format string
     * @param ... Variable arguments
     * @retval fw_error_handler_result_t Error handler result
     */
    fw_error_handler_result_t fw_error_report(fw_error_code_t code,
                                              fw_error_severity_t severity,
                                              const char *module,
                                              const char *file,
                                              uint32_t line,
                                              const char *function,
                                              const char *format, ...);

    /**
     * @brief Set error handler callback
     * @param callback Error handler callback function
     * @retval fw_error_handler_result_t Error handler result
     */
    fw_error_handler_result_t fw_error_handler_set_callback(fw_error_handler_callback_t callback);

    /**
     * @brief Get error code string representation
     * @param code Error code
     * @retval const char* Error code string
     */
    const char *fw_error_get_code_string(fw_error_code_t code);

    /**
     * @brief Get error severity string representation
     * @param severity Error severity
     * @retval const char* Error severity string
     */
    const char *fw_error_get_severity_string(fw_error_severity_t severity);

    /**
     * @brief Get last error information
     * @retval const fw_error_info_t* Pointer to last error info (NULL if no errors)
     */
    const fw_error_info_t *fw_error_get_last_error(void);

    /**
     * @brief Get error count by severity
     * @param severity Error severity
     * @retval uint32_t Number of errors with specified severity
     */
    uint32_t fw_error_get_count_by_severity(fw_error_severity_t severity);

    /**
     * @brief Get total error count
     * @retval uint32_t Total number of errors
     */
    uint32_t fw_error_get_total_count(void);

    /**
     * @brief Clear error history
     * @retval fw_error_handler_result_t Error handler result
     */
    fw_error_handler_result_t fw_error_clear_history(void);

    /**
     * @brief Get error history
     * @param history_buffer Buffer to store error history
     * @param buffer_size Size of history buffer
     * @param count Pointer to store actual number of errors returned
     * @retval fw_error_handler_result_t Error handler result
     */
    fw_error_handler_result_t fw_error_get_history(fw_error_info_t *history_buffer,
                                                   uint32_t buffer_size,
                                                   uint32_t *count);

    /**
     * @brief Default error handler callback
     * @param error_info Error information
     */
    void fw_error_default_handler(const fw_error_info_t *error_info);

#ifdef __cplusplus
}
#endif

#endif /* __FW_ERROR_H__ */
