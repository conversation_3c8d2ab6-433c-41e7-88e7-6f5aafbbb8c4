/**
 ******************************************************************************
 * @file    fw_config.h
 * @brief   框架配置管理头文件
 ******************************************************************************
 * @attention
 *
 * 此文件包含STM32G474嵌入式开发框架的配置定义和管理函数。
 *
 ******************************************************************************
 */

#ifndef __FW_CONFIG_H__
#define __FW_CONFIG_H__

#ifdef __cplusplus
extern "C"
{
#endif

/* 包含文件 ------------------------------------------------------------------*/
#include "main.h"
#include <stdint.h>
#include <stdbool.h>

    /* 导出类型 ------------------------------------------------------------------*/

    /**
     * @brief 框架配置结构体
     */
    typedef struct
    {
        /* 系统配置 */
        uint32_t system_clock_freq;
        uint32_t tick_rate_hz;

        /* 任务配置 */
        uint16_t default_stack_size;
        uint8_t max_task_priority;
        uint8_t default_task_priority;

        /* 内存配置 */
        uint32_t heap_size;
        uint16_t queue_registry_size;

        /* 调试配置 */
        bool debug_enabled;
        uint8_t log_level;

        /* 硬件配置 */
        struct
        {
            bool gpio_enabled;
            bool uart_enabled;
            bool timer_enabled;
            bool adc_enabled;
        } hardware;

    } fw_config_t;

    /**
     * @brief 日志级别枚举
     */
    typedef enum
    {
        FW_LOG_LEVEL_ERROR = 0,
        FW_LOG_LEVEL_WARN = 1,
        FW_LOG_LEVEL_INFO = 2,
        FW_LOG_LEVEL_DEBUG = 3
    } fw_log_level_t;

    /**
     * @brief 配置结果枚举
     */
    typedef enum
    {
        FW_CONFIG_OK = 0,
        FW_CONFIG_ERROR,
        FW_CONFIG_INVALID_PARAM
    } fw_config_result_t;

/* 导出常量 ------------------------------------------------------------------*/

/* 默认配置值 */
#define FW_DEFAULT_SYSTEM_CLOCK_FREQ 170000000UL /* 170MHz */
#define FW_DEFAULT_TICK_RATE_HZ 1000UL           /* 1ms tick */
#define FW_DEFAULT_STACK_SIZE 512U               /* 512 words */
#define FW_DEFAULT_MAX_PRIORITY 56U              /* CMSIS-RTOS v2 max */
#define FW_DEFAULT_TASK_PRIORITY 24U             /* Normal priority */
#define FW_DEFAULT_HEAP_SIZE (8 * 1024)          /* 8KB heap */
#define FW_DEFAULT_QUEUE_REGISTRY_SIZE 8U
#define FW_DEFAULT_LOG_LEVEL FW_LOG_LEVEL_INFO

/* 配置验证宏 */
#define FW_IS_VALID_PRIORITY(p) ((p) <= FW_DEFAULT_MAX_PRIORITY)
#define FW_IS_VALID_STACK_SIZE(s) ((s) >= 128U && (s) <= 4096U)
#define FW_IS_VALID_LOG_LEVEL(l) ((l) <= FW_LOG_LEVEL_DEBUG)

/* 导出宏 --------------------------------------------------------------------*/

/**
 * @brief 获取当前框架配置
 */
#define FW_GET_CONFIG() fw_config_get()

/**
 * @brief 检查是否启用调试模式
 */
#define FW_IS_DEBUG_ENABLED() (FW_GET_CONFIG()->debug_enabled)

/**
 * @brief 获取当前日志级别
 */
#define FW_GET_LOG_LEVEL() (FW_GET_CONFIG()->log_level)

    /* 导出函数原型 --------------------------------------------------------------*/

    /**
     * @brief 使用默认值初始化框架配置
     * @retval fw_config_result_t 配置结果
     */
    fw_config_result_t fw_config_init(void);

    /**
     * @brief 获取当前配置的指针
     * @retval fw_config_t* 配置结构体指针
     */
    const fw_config_t *fw_config_get(void);

    /**
     * @brief 设置系统时钟频率
     * @param freq 时钟频率（Hz）
     * @retval fw_config_result_t 配置结果
     */
    fw_config_result_t fw_config_set_system_clock(uint32_t freq);

    /**
     * @brief Set default task stack size
     * @param stack_size Stack size in words
     * @retval fw_config_result_t Configuration result
     */
    fw_config_result_t fw_config_set_stack_size(uint16_t stack_size);

    /**
     * @brief Set log level
     * @param level Log level
     * @retval fw_config_result_t Configuration result
     */
    fw_config_result_t fw_config_set_log_level(fw_log_level_t level);

    /**
     * @brief Enable/disable debug mode
     * @param enabled Debug mode state
     * @retval fw_config_result_t Configuration result
     */
    fw_config_result_t fw_config_set_debug_mode(bool enabled);

    /**
     * @brief Enable/disable hardware module
     * @param module Hardware module name ("gpio", "uart", "timer", "adc")
     * @param enabled Module state
     * @retval fw_config_result_t Configuration result
     */
    fw_config_result_t fw_config_set_hardware_module(const char *module, bool enabled);

    /**
     * @brief Validate current configuration
     * @retval fw_config_result_t Validation result
     */
    fw_config_result_t fw_config_validate(void);

    /**
     * @brief Reset configuration to default values
     * @retval fw_config_result_t Configuration result
     */
    fw_config_result_t fw_config_reset_to_default(void);

#ifdef __cplusplus
}
#endif

#endif /* __FW_CONFIG_H__ */
