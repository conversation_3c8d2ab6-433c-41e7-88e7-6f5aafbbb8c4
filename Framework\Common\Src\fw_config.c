/**
  ******************************************************************************
  * @file    fw_config.c
  * @brief   Framework configuration management implementation
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "fw_config.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/**
 * @brief Global framework configuration instance
 */
static fw_config_t g_fw_config;

/**
 * @brief Configuration initialization flag
 */
static bool g_config_initialized = false;

/* Private function prototypes -----------------------------------------------*/
static void fw_config_load_defaults(void);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief Initialize framework configuration with default values
 */
fw_config_result_t fw_config_init(void)
{
    if (g_config_initialized) {
        return FW_CONFIG_OK;
    }
    
    fw_config_load_defaults();
    g_config_initialized = true;
    
    return FW_CONFIG_OK;
}

/**
 * @brief Get pointer to current configuration
 */
const fw_config_t* fw_config_get(void)
{
    if (!g_config_initialized) {
        fw_config_init();
    }
    
    return &g_fw_config;
}

/**
 * @brief Set system clock frequency
 */
fw_config_result_t fw_config_set_system_clock(uint32_t freq)
{
    if (freq == 0) {
        return FW_CONFIG_INVALID_PARAM;
    }
    
    g_fw_config.system_clock_freq = freq;
    return FW_CONFIG_OK;
}

/**
 * @brief Set default task stack size
 */
fw_config_result_t fw_config_set_stack_size(uint16_t stack_size)
{
    if (!FW_IS_VALID_STACK_SIZE(stack_size)) {
        return FW_CONFIG_INVALID_PARAM;
    }
    
    g_fw_config.default_stack_size = stack_size;
    return FW_CONFIG_OK;
}

/**
 * @brief Set log level
 */
fw_config_result_t fw_config_set_log_level(fw_log_level_t level)
{
    if (!FW_IS_VALID_LOG_LEVEL(level)) {
        return FW_CONFIG_INVALID_PARAM;
    }
    
    g_fw_config.log_level = level;
    return FW_CONFIG_OK;
}

/**
 * @brief Enable/disable debug mode
 */
fw_config_result_t fw_config_set_debug_mode(bool enabled)
{
    g_fw_config.debug_enabled = enabled;
    return FW_CONFIG_OK;
}

/**
 * @brief Enable/disable hardware module
 */
fw_config_result_t fw_config_set_hardware_module(const char* module, bool enabled)
{
    if (module == NULL) {
        return FW_CONFIG_INVALID_PARAM;
    }
    
    if (strcmp(module, "gpio") == 0) {
        g_fw_config.hardware.gpio_enabled = enabled;
    } else if (strcmp(module, "uart") == 0) {
        g_fw_config.hardware.uart_enabled = enabled;
    } else if (strcmp(module, "timer") == 0) {
        g_fw_config.hardware.timer_enabled = enabled;
    } else if (strcmp(module, "adc") == 0) {
        g_fw_config.hardware.adc_enabled = enabled;
    } else {
        return FW_CONFIG_INVALID_PARAM;
    }
    
    return FW_CONFIG_OK;
}

/**
 * @brief Validate current configuration
 */
fw_config_result_t fw_config_validate(void)
{
    const fw_config_t* config = fw_config_get();
    
    /* Validate system configuration */
    if (config->system_clock_freq == 0) {
        return FW_CONFIG_ERROR;
    }
    
    if (config->tick_rate_hz == 0) {
        return FW_CONFIG_ERROR;
    }
    
    /* Validate task configuration */
    if (!FW_IS_VALID_STACK_SIZE(config->default_stack_size)) {
        return FW_CONFIG_ERROR;
    }
    
    if (!FW_IS_VALID_PRIORITY(config->max_task_priority)) {
        return FW_CONFIG_ERROR;
    }
    
    if (!FW_IS_VALID_PRIORITY(config->default_task_priority)) {
        return FW_CONFIG_ERROR;
    }
    
    /* Validate memory configuration */
    if (config->heap_size == 0) {
        return FW_CONFIG_ERROR;
    }
    
    /* Validate debug configuration */
    if (!FW_IS_VALID_LOG_LEVEL(config->log_level)) {
        return FW_CONFIG_ERROR;
    }
    
    return FW_CONFIG_OK;
}

/**
 * @brief Reset configuration to default values
 */
fw_config_result_t fw_config_reset_to_default(void)
{
    fw_config_load_defaults();
    return FW_CONFIG_OK;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Load default configuration values
 */
static void fw_config_load_defaults(void)
{
    /* System Configuration */
    g_fw_config.system_clock_freq = FW_DEFAULT_SYSTEM_CLOCK_FREQ;
    g_fw_config.tick_rate_hz = FW_DEFAULT_TICK_RATE_HZ;
    
    /* Task Configuration */
    g_fw_config.default_stack_size = FW_DEFAULT_STACK_SIZE;
    g_fw_config.max_task_priority = FW_DEFAULT_MAX_PRIORITY;
    g_fw_config.default_task_priority = FW_DEFAULT_TASK_PRIORITY;
    
    /* Memory Configuration */
    g_fw_config.heap_size = FW_DEFAULT_HEAP_SIZE;
    g_fw_config.queue_registry_size = FW_DEFAULT_QUEUE_REGISTRY_SIZE;
    
    /* Debug Configuration */
    g_fw_config.debug_enabled = true;
    g_fw_config.log_level = FW_DEFAULT_LOG_LEVEL;
    
    /* Hardware Configuration */
    g_fw_config.hardware.gpio_enabled = true;
    g_fw_config.hardware.uart_enabled = true;
    g_fw_config.hardware.timer_enabled = true;
    g_fw_config.hardware.adc_enabled = false;
}
