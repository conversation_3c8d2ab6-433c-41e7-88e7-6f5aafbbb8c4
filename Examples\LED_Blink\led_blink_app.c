/**
 ******************************************************************************
 * @file    led_blink_app.c
 * @brief   LED闪烁应用实现
 ******************************************************************************
 */

/* 包含文件 ------------------------------------------------------------------*/
#include "led_blink_app.h"
#include "fw_logger.h"
#include <string.h>

/* 私有类型定义 --------------------------------------------------------------*/
/* 私有定义 ------------------------------------------------------------------*/
#define LED_APP_TAG "LED_APP"

/* 私有宏 --------------------------------------------------------------------*/
/* 私有变量 ------------------------------------------------------------------*/

/**
 * @brief 默认LED应用实例
 */
static led_app_context_t g_default_led_app = {0};

/* 私有函数原型 --------------------------------------------------------------*/
static fw_rtos_result_t led_state_off_entry(const fw_event_t *event);
static fw_rtos_result_t led_state_off_event(const fw_event_t *event);
static fw_rtos_result_t led_state_on_entry(const fw_event_t *event);
static fw_rtos_result_t led_state_on_event(const fw_event_t *event);
static fw_rtos_result_t led_state_slow_blink_entry(const fw_event_t *event);
static fw_rtos_result_t led_state_slow_blink_event(const fw_event_t *event);
static fw_rtos_result_t led_state_fast_blink_entry(const fw_event_t *event);
static fw_rtos_result_t led_state_fast_blink_event(const fw_event_t *event);
static fw_rtos_result_t led_state_error_entry(const fw_event_t *event);
static fw_rtos_result_t led_state_error_event(const fw_event_t *event);

static led_app_result_t led_app_set_physical_state(led_app_context_t *led_ctx, bool state);
static led_app_result_t led_app_toggle_physical_state(led_app_context_t *led_ctx);
static led_app_result_t led_app_send_event(led_app_context_t *led_ctx, led_event_id_t event_id);

/* 状态机定义 ----------------------------------------------------------------*/

/**
 * @brief LED状态机状态定义
 */
static const fw_state_t led_states[] = {
    FW_STATE_DEFINE(LED_STATE_OFF, "OFF", led_state_off_entry, NULL, led_state_off_event),
    FW_STATE_DEFINE(LED_STATE_ON, "ON", led_state_on_entry, NULL, led_state_on_event),
    FW_STATE_DEFINE(LED_STATE_BLINKING_SLOW, "SLOW_BLINK", led_state_slow_blink_entry, NULL, led_state_slow_blink_event),
    FW_STATE_DEFINE(LED_STATE_BLINKING_FAST, "FAST_BLINK", led_state_fast_blink_entry, NULL, led_state_fast_blink_event),
    FW_STATE_DEFINE(LED_STATE_ERROR, "ERROR", led_state_error_entry, NULL, led_state_error_event)};

/**
 * @brief LED状态机转换定义
 */
static const fw_transition_t led_transitions[] = {
    /* 从OFF状态的转换 */
    FW_TRANSITION_DEFINE(LED_STATE_OFF, LED_STATE_ON, FW_EVENT_TYPE_USER, LED_EVENT_TURN_ON, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_OFF, LED_STATE_BLINKING_SLOW, FW_EVENT_TYPE_USER, LED_EVENT_START_SLOW_BLINK, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_OFF, LED_STATE_BLINKING_FAST, FW_EVENT_TYPE_USER, LED_EVENT_START_FAST_BLINK, NULL),

    /* 从ON状态的转换 */
    FW_TRANSITION_DEFINE(LED_STATE_ON, LED_STATE_OFF, FW_EVENT_TYPE_USER, LED_EVENT_TURN_OFF, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_ON, LED_STATE_BLINKING_SLOW, FW_EVENT_TYPE_USER, LED_EVENT_START_SLOW_BLINK, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_ON, LED_STATE_BLINKING_FAST, FW_EVENT_TYPE_USER, LED_EVENT_START_FAST_BLINK, NULL),

    /* 从SLOW_BLINK状态的转换 */
    FW_TRANSITION_DEFINE(LED_STATE_BLINKING_SLOW, LED_STATE_OFF, FW_EVENT_TYPE_USER, LED_EVENT_TURN_OFF, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_BLINKING_SLOW, LED_STATE_ON, FW_EVENT_TYPE_USER, LED_EVENT_TURN_ON, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_BLINKING_SLOW, LED_STATE_BLINKING_FAST, FW_EVENT_TYPE_USER, LED_EVENT_START_FAST_BLINK, NULL),

    /* 从FAST_BLINK状态的转换 */
    FW_TRANSITION_DEFINE(LED_STATE_BLINKING_FAST, LED_STATE_OFF, FW_EVENT_TYPE_USER, LED_EVENT_TURN_OFF, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_BLINKING_FAST, LED_STATE_ON, FW_EVENT_TYPE_USER, LED_EVENT_TURN_ON, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_BLINKING_FAST, LED_STATE_BLINKING_SLOW, FW_EVENT_TYPE_USER, LED_EVENT_START_SLOW_BLINK, NULL),

    /* 错误状态转换 */
    FW_TRANSITION_DEFINE(LED_STATE_OFF, LED_STATE_ERROR, FW_EVENT_TYPE_ERROR, LED_EVENT_ERROR_OCCURRED, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_ON, LED_STATE_ERROR, FW_EVENT_TYPE_ERROR, LED_EVENT_ERROR_OCCURRED, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_BLINKING_SLOW, LED_STATE_ERROR, FW_EVENT_TYPE_ERROR, LED_EVENT_ERROR_OCCURRED, NULL),
    FW_TRANSITION_DEFINE(LED_STATE_BLINKING_FAST, LED_STATE_ERROR, FW_EVENT_TYPE_ERROR, LED_EVENT_ERROR_OCCURRED, NULL)};

/* 导出函数 ------------------------------------------------------------------*/

/**
 * @brief 初始化LED应用
 */
led_app_result_t led_app_init(led_app_context_t *led_ctx, const led_config_t *config)
{
    fw_app_result_t app_result;
    fw_gpio_result_t gpio_result;

    /* 参数验证 */
    if (led_ctx == NULL || config == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, LED_APP_TAG, "参数为空");
        return LED_APP_INVALID_PARAM;
    }

    if (led_ctx->initialized)
    {
        FW_LOG_WARN(LED_APP_TAG, "LED应用已初始化");
        return LED_APP_OK;
    }

    /* 初始化上下文 */
    memset(led_ctx, 0, sizeof(led_app_context_t));
    memcpy(&led_ctx->config, config, sizeof(led_config_t));

    /* 初始化GPIO */
    gpio_result = fw_gpio_pin_init(&led_ctx->config.gpio_handle);
    if (gpio_result != FW_GPIO_OK)
    {
        FW_ERROR_REPORT(FW_ERROR_HW_GPIO_INIT_FAILED, LED_APP_TAG, "GPIO初始化失败");
        return LED_APP_GPIO_ERROR;
    }

    /* 初始化状态机 */
    app_result = fw_state_machine_init(&led_ctx->state_machine,
                                       "LED_StateMachine",
                                       led_states,
                                       sizeof(led_states) / sizeof(led_states[0]),
                                       led_transitions,
                                       sizeof(led_transitions) / sizeof(led_transitions[0]),
                                       LED_STATE_OFF);

    if (app_result != FW_APP_OK)
    {
        FW_ERROR_REPORT(FW_ERROR_APP_OPERATION_FAILED, LED_APP_TAG, "状态机初始化失败");
        fw_gpio_pin_deinit(&led_ctx->config.gpio_handle);
        return LED_APP_ERROR;
    }

    /* 初始化LED状态 */
    led_ctx->current_led_state = LED_STATE_OFF;
    led_ctx->led_physical_state = false;
    led_ctx->last_toggle_time = 0;
    led_ctx->blink_period = config->slow_blink_period_ms;

    /* 设置初始物理状态 */
    led_app_set_physical_state(led_ctx, false);

    led_ctx->initialized = true;

    FW_LOG_INFO(LED_APP_TAG, "LED应用初始化完成");

    return LED_APP_OK;
}

/**
 * @brief 反初始化LED应用
 */
led_app_result_t led_app_deinit(led_app_context_t *led_ctx)
{
    /* 参数验证 */
    if (led_ctx == NULL)
    {
        return LED_APP_INVALID_PARAM;
    }

    if (!led_ctx->initialized)
    {
        return LED_APP_OK;
    }

    /* 关闭LED */
    led_app_set_physical_state(led_ctx, false);

    /* 反初始化GPIO */
    fw_gpio_pin_deinit(&led_ctx->config.gpio_handle);

    led_ctx->initialized = false;

    FW_LOG_INFO(LED_APP_TAG, "LED应用反初始化完成");

    return LED_APP_OK;
}

/**
 * @brief 启动LED应用任务
 */
led_app_result_t led_app_start(led_app_context_t *led_ctx)
{
    fw_task_config_t task_config;
    fw_app_result_t app_result;

    /* 参数验证 */
    if (led_ctx == NULL)
    {
        return LED_APP_INVALID_PARAM;
    }

    if (!led_ctx->initialized)
    {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, LED_APP_TAG, "LED应用未初始化");
        return LED_APP_NOT_INITIALIZED;
    }

    /* 配置任务 */
    task_config = (fw_task_config_t)FW_TASK_CONFIG_INIT(
        LED_TASK_NAME,
        led_app_task_function,
        LED_TASK_PRIORITY,
        LED_TASK_STACK_SIZE,
        led_ctx);

    /* 创建应用任务 */
    app_result = fw_app_task_create(&led_ctx->app_task, &task_config, &led_ctx->state_machine);
    if (app_result != FW_APP_OK)
    {
        FW_ERROR_REPORT(FW_ERROR_SYSTEM_TASK_CREATION_FAILED, LED_APP_TAG, "创建LED任务失败");
        return LED_APP_ERROR;
    }

    /* 如果配置为自动启动，则开始慢速闪烁 */
    if (led_ctx->config.auto_start)
    {
        led_app_start_slow_blink(led_ctx);
    }

    FW_LOG_INFO(LED_APP_TAG, "LED应用任务启动完成");

    return LED_APP_OK;
}

/**
 * @brief 停止LED应用任务
 */
led_app_result_t led_app_stop(led_app_context_t *led_ctx)
{
    fw_app_result_t app_result;

    /* 参数验证 */
    if (led_ctx == NULL)
    {
        return LED_APP_INVALID_PARAM;
    }

    /* 关闭LED */
    led_app_turn_off(led_ctx);

    /* 删除应用任务 */
    app_result = fw_app_task_delete(&led_ctx->app_task);
    if (app_result != FW_APP_OK)
    {
        FW_ERROR_REPORT(FW_ERROR_APP_OPERATION_FAILED, LED_APP_TAG, "删除LED任务失败");
        return LED_APP_ERROR;
    }

    FW_LOG_INFO(LED_APP_TAG, "LED应用任务停止完成");

    return LED_APP_OK;
}

/**
 * @brief 打开LED
 */
led_app_result_t led_app_turn_on(led_app_context_t *led_ctx)
{
    return led_app_send_event(led_ctx, LED_EVENT_TURN_ON);
}

/**
 * @brief 关闭LED
 */
led_app_result_t led_app_turn_off(led_app_context_t *led_ctx)
{
    return led_app_send_event(led_ctx, LED_EVENT_TURN_OFF);
}

/**
 * @brief 开始慢速闪烁
 */
led_app_result_t led_app_start_slow_blink(led_app_context_t *led_ctx)
{
    return led_app_send_event(led_ctx, LED_EVENT_START_SLOW_BLINK);
}

/**
 * @brief 开始快速闪烁
 */
led_app_result_t led_app_start_fast_blink(led_app_context_t *led_ctx)
{
    return led_app_send_event(led_ctx, LED_EVENT_START_FAST_BLINK);
}

/**
 * @brief 获取LED当前状态
 */
led_state_t led_app_get_state(const led_app_context_t *led_ctx)
{
    if (led_ctx == NULL || !led_ctx->initialized)
    {
        return LED_STATE_ERROR;
    }

    return led_ctx->current_led_state;
}

/**
 * @brief 获取LED物理状态
 */
bool led_app_get_physical_state(const led_app_context_t *led_ctx)
{
    if (led_ctx == NULL || !led_ctx->initialized)
    {
        return false;
    }

    return led_ctx->led_physical_state;
}

/**
 * @brief LED任务函数
 */
void led_app_task_function(void *argument)
{
    led_app_context_t *led_ctx = (led_app_context_t *)argument;
    fw_event_t timer_event;
    uint32_t current_time;

    if (led_ctx == NULL)
    {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, LED_APP_TAG, "任务参数为空");
        return;
    }

    FW_LOG_INFO(LED_APP_TAG, "LED任务开始运行");

    /* 任务主循环 */
    while (1)
    {
        /* 运行应用任务框架 */
        fw_app_task_run(&led_ctx->app_task);

        /* 检查是否需要发送定时器事件（用于闪烁） */
        current_time = fw_rtos_get_tick_count();
        if ((led_ctx->current_led_state == LED_STATE_BLINKING_SLOW ||
             led_ctx->current_led_state == LED_STATE_BLINKING_FAST) &&
            (current_time - led_ctx->last_toggle_time >= led_ctx->blink_period))
        {

            /* 创建定时器事件 */
            timer_event = FW_EVENT_INIT(FW_EVENT_TYPE_TIMER, LED_EVENT_TIMER_TICK, NULL, 0);

            /* 发送事件到状态机 */
            fw_state_machine_process_event(&led_ctx->state_machine, &timer_event);
        }

        /* 短暂延时 */
        fw_task_delay(10);
    }
}

/**
 * @brief 创建默认LED应用实例
 */
led_app_context_t *led_app_create_default_instance(void)
{
    led_config_t default_config = LED_CONFIG_DEFAULT();

    if (led_app_init(&g_default_led_app, &default_config) == LED_APP_OK)
    {
        return &g_default_led_app;
    }

    return NULL;
}

/**
 * @brief 获取LED应用示例的使用说明
 */
const char *led_app_get_usage_info(void)
{
    return "LED闪烁应用使用说明:\n"
           "1. 调用 led_app_create_default_instance() 创建默认实例\n"
           "2. 调用 led_app_start() 启动任务\n"
           "3. 使用以下函数控制LED:\n"
           "   - led_app_turn_on(): 打开LED\n"
           "   - led_app_turn_off(): 关闭LED\n"
           "   - led_app_start_slow_blink(): 慢速闪烁\n"
           "   - led_app_start_fast_blink(): 快速闪烁\n"
           "4. 调用 led_app_stop() 停止任务\n"
           "5. 调用 led_app_deinit() 清理资源\n";
}

/* 私有函数实现 --------------------------------------------------------------*/

/**
 * @brief 设置LED物理状态
 */
static led_app_result_t led_app_set_physical_state(led_app_context_t *led_ctx, bool state)
{
    fw_gpio_result_t gpio_result;

    if (led_ctx == NULL)
    {
        return LED_APP_INVALID_PARAM;
    }

    gpio_result = fw_gpio_pin_write(&led_ctx->config.gpio_handle,
                                    state ? FW_GPIO_PIN_SET : FW_GPIO_PIN_RESET);

    if (gpio_result == FW_GPIO_OK)
    {
        led_ctx->led_physical_state = state;
        FW_LOG_DEBUG(LED_APP_TAG, "LED物理状态设置为: %s", state ? "ON" : "OFF");
        return LED_APP_OK;
    }
    else
    {
        FW_ERROR_REPORT(FW_ERROR_HW_GPIO_INIT_FAILED, LED_APP_TAG, "设置GPIO状态失败");
        return LED_APP_GPIO_ERROR;
    }
}

/**
 * @brief 切换LED物理状态
 */
static led_app_result_t led_app_toggle_physical_state(led_app_context_t *led_ctx)
{
    if (led_ctx == NULL)
    {
        return LED_APP_INVALID_PARAM;
    }

    return led_app_set_physical_state(led_ctx, !led_ctx->led_physical_state);
}

/**
 * @brief 发送事件到LED应用
 */
static led_app_result_t led_app_send_event(led_app_context_t *led_ctx, led_event_id_t event_id)
{
    fw_event_t event;
    fw_app_result_t app_result;

    if (led_ctx == NULL)
    {
        return LED_APP_INVALID_PARAM;
    }

    if (!led_ctx->initialized)
    {
        return LED_APP_NOT_INITIALIZED;
    }

    /* 创建事件 */
    event = FW_EVENT_INIT(FW_EVENT_TYPE_USER, event_id, NULL, 0);

    /* 发送事件 */
    app_result = fw_app_task_send_event(&led_ctx->app_task, &event);

    if (app_result != FW_APP_OK)
    {
        FW_ERROR_REPORT(FW_ERROR_APP_OPERATION_FAILED, LED_APP_TAG,
                        "发送事件失败: %d", event_id);
        return LED_APP_ERROR;
    }

    return LED_APP_OK;
}

/* 状态机状态处理函数 --------------------------------------------------------*/

/**
 * @brief OFF状态进入处理
 */
static fw_rtos_result_t led_state_off_entry(const fw_event_t *event)
{
    led_app_context_t *led_ctx = (led_app_context_t *)event->data;

    FW_LOG_INFO(LED_APP_TAG, "进入OFF状态");

    if (led_ctx != NULL)
    {
        led_ctx->current_led_state = LED_STATE_OFF;
        led_app_set_physical_state(led_ctx, false);
    }

    return FW_RTOS_OK;
}

/**
 * @brief OFF状态事件处理
 */
static fw_rtos_result_t led_state_off_event(const fw_event_t *event)
{
    /* OFF状态下不需要特殊的事件处理，状态转换由状态机自动处理 */
    return FW_RTOS_OK;
}

/**
 * @brief ON状态进入处理
 */
static fw_rtos_result_t led_state_on_entry(const fw_event_t *event)
{
    led_app_context_t *led_ctx = (led_app_context_t *)event->data;

    FW_LOG_INFO(LED_APP_TAG, "进入ON状态");

    if (led_ctx != NULL)
    {
        led_ctx->current_led_state = LED_STATE_ON;
        led_app_set_physical_state(led_ctx, true);
    }

    return FW_RTOS_OK;
}

/**
 * @brief ON状态事件处理
 */
static fw_rtos_result_t led_state_on_event(const fw_event_t *event)
{
    /* ON状态下不需要特殊的事件处理，状态转换由状态机自动处理 */
    return FW_RTOS_OK;
}

/**
 * @brief 慢速闪烁状态进入处理
 */
static fw_rtos_result_t led_state_slow_blink_entry(const fw_event_t *event)
{
    led_app_context_t *led_ctx = (led_app_context_t *)event->data;

    FW_LOG_INFO(LED_APP_TAG, "进入慢速闪烁状态");

    if (led_ctx != NULL)
    {
        led_ctx->current_led_state = LED_STATE_BLINKING_SLOW;
        led_ctx->blink_period = led_ctx->config.slow_blink_period_ms;
        led_ctx->last_toggle_time = fw_rtos_get_tick_count();
        led_app_set_physical_state(led_ctx, true);
    }

    return FW_RTOS_OK;
}

/**
 * @brief 慢速闪烁状态事件处理
 */
static fw_rtos_result_t led_state_slow_blink_event(const fw_event_t *event)
{
    led_app_context_t *led_ctx = (led_app_context_t *)event->data;

    if (event->type == FW_EVENT_TYPE_TIMER && event->id == LED_EVENT_TIMER_TICK)
    {
        if (led_ctx != NULL)
        {
            led_app_toggle_physical_state(led_ctx);
            led_ctx->last_toggle_time = fw_rtos_get_tick_count();
        }
    }

    return FW_RTOS_OK;
}

/**
 * @brief 快速闪烁状态进入处理
 */
static fw_rtos_result_t led_state_fast_blink_entry(const fw_event_t *event)
{
    led_app_context_t *led_ctx = (led_app_context_t *)event->data;

    FW_LOG_INFO(LED_APP_TAG, "进入快速闪烁状态");

    if (led_ctx != NULL)
    {
        led_ctx->current_led_state = LED_STATE_BLINKING_FAST;
        led_ctx->blink_period = led_ctx->config.fast_blink_period_ms;
        led_ctx->last_toggle_time = fw_rtos_get_tick_count();
        led_app_set_physical_state(led_ctx, true);
    }

    return FW_RTOS_OK;
}

/**
 * @brief 快速闪烁状态事件处理
 */
static fw_rtos_result_t led_state_fast_blink_event(const fw_event_t *event)
{
    led_app_context_t *led_ctx = (led_app_context_t *)event->data;

    if (event->type == FW_EVENT_TYPE_TIMER && event->id == LED_EVENT_TIMER_TICK)
    {
        if (led_ctx != NULL)
        {
            led_app_toggle_physical_state(led_ctx);
            led_ctx->last_toggle_time = fw_rtos_get_tick_count();
        }
    }

    return FW_RTOS_OK;
}

/**
 * @brief 错误状态进入处理
 */
static fw_rtos_result_t led_state_error_entry(const fw_event_t *event)
{
    led_app_context_t *led_ctx = (led_app_context_t *)event->data;

    FW_LOG_ERROR(LED_APP_TAG, "进入错误状态");

    if (led_ctx != NULL)
    {
        led_ctx->current_led_state = LED_STATE_ERROR;
        led_app_set_physical_state(led_ctx, false);
    }

    return FW_RTOS_OK;
}

/**
 * @brief 错误状态事件处理
 */
static fw_rtos_result_t led_state_error_event(const fw_event_t *event)
{
    /* 错误状态下只能通过重新初始化来恢复 */
    return FW_RTOS_OK;
}
