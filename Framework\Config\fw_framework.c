/**
  ******************************************************************************
  * @file    fw_framework.c
  * @brief   框架主实现文件
  ******************************************************************************
  */

/* 包含文件 ------------------------------------------------------------------*/
#include "fw_framework.h"
#include <string.h>
#include <stdio.h>

/* 私有类型定义 --------------------------------------------------------------*/

/**
 * @brief 框架上下文结构体
 */
typedef struct {
    bool initialized;
    fw_framework_config_t config;
    uint32_t init_timestamp;
} fw_framework_context_t;

/* 私有定义 ------------------------------------------------------------------*/
#define FW_FRAMEWORK_TAG                "FRAMEWORK"

/* 私有宏 --------------------------------------------------------------------*/
/* 私有变量 ------------------------------------------------------------------*/

/**
 * @brief 全局框架上下文
 */
static fw_framework_context_t g_framework_ctx = {0};

/* 私有函数原型 --------------------------------------------------------------*/
static fw_framework_result_t fw_framework_init_config(const fw_framework_config_t* config);
static fw_framework_result_t fw_framework_init_logger(void);
static fw_framework_result_t fw_framework_init_error_handler(void);
static fw_framework_result_t fw_framework_init_hardware(void);
static fw_framework_result_t fw_framework_init_rtos(void);
static fw_framework_result_t fw_framework_init_app(void);

/* 导出函数 ------------------------------------------------------------------*/

/**
 * @brief 初始化框架
 */
fw_framework_result_t fw_framework_init(const fw_framework_config_t* config)
{
    fw_framework_result_t result;
    
    /* 检查是否已初始化 */
    if (g_framework_ctx.initialized) {
        return FW_FRAMEWORK_ALREADY_INITIALIZED;
    }
    
    /* 初始化配置 */
    result = fw_framework_init_config(config);
    if (result != FW_FRAMEWORK_OK) {
        return result;
    }
    
    /* 初始化日志系统 */
    if (g_framework_ctx.config.enable_logger) {
        result = fw_framework_init_logger();
        if (result != FW_FRAMEWORK_OK) {
            return result;
        }
    }
    
    /* 初始化错误处理系统 */
    if (g_framework_ctx.config.enable_error_handler) {
        result = fw_framework_init_error_handler();
        if (result != FW_FRAMEWORK_OK) {
            return result;
        }
    }
    
    /* 记录框架启动 */
    if (g_framework_ctx.config.enable_logger) {
        FW_LOG_INFO(FW_FRAMEWORK_TAG, "STM32G474嵌入式开发框架启动");
        FW_LOG_INFO(FW_FRAMEWORK_TAG, "版本: %s", FW_FRAMEWORK_VERSION_STRING);
    }
    
    /* 初始化硬件抽象层 */
    result = fw_framework_init_hardware();
    if (result != FW_FRAMEWORK_OK) {
        return result;
    }
    
    /* 初始化RTOS封装层 */
    if (g_framework_ctx.config.enable_rtos_wrapper) {
        result = fw_framework_init_rtos();
        if (result != FW_FRAMEWORK_OK) {
            return result;
        }
    }
    
    /* 初始化应用层框架 */
    if (g_framework_ctx.config.enable_app_framework) {
        result = fw_framework_init_app();
        if (result != FW_FRAMEWORK_OK) {
            return result;
        }
    }
    
    /* 设置初始化标志和时间戳 */
    g_framework_ctx.initialized = true;
    g_framework_ctx.init_timestamp = osKernelGetTickCount();
    
    if (g_framework_ctx.config.enable_logger) {
        FW_LOG_INFO(FW_FRAMEWORK_TAG, "框架初始化完成");
    }
    
    return FW_FRAMEWORK_OK;
}

/**
 * @brief 反初始化框架
 */
fw_framework_result_t fw_framework_deinit(void)
{
    if (!g_framework_ctx.initialized) {
        return FW_FRAMEWORK_OK;
    }
    
    if (g_framework_ctx.config.enable_logger) {
        FW_LOG_INFO(FW_FRAMEWORK_TAG, "开始框架反初始化");
    }
    
    /* 反初始化应用层 */
    if (g_framework_ctx.config.enable_app_framework) {
        fw_app_deinit();
    }
    
    /* 反初始化RTOS封装层 */
    if (g_framework_ctx.config.enable_rtos_wrapper) {
        fw_rtos_deinit();
    }
    
    /* 反初始化硬件抽象层 */
    fw_gpio_deinit();
    
    /* 反初始化错误处理系统 */
    if (g_framework_ctx.config.enable_error_handler) {
        fw_error_handler_deinit();
    }
    
    /* 反初始化日志系统 */
    if (g_framework_ctx.config.enable_logger) {
        FW_LOG_INFO(FW_FRAMEWORK_TAG, "框架反初始化完成");
        fw_logger_deinit();
    }
    
    g_framework_ctx.initialized = false;
    
    return FW_FRAMEWORK_OK;
}

/**
 * @brief 检查框架是否已初始化
 */
bool fw_framework_is_initialized(void)
{
    return g_framework_ctx.initialized;
}

/**
 * @brief 获取框架版本信息
 */
const char* fw_framework_get_version(void)
{
    return FW_FRAMEWORK_VERSION_STRING;
}

/**
 * @brief 获取框架配置信息
 */
const fw_framework_config_t* fw_framework_get_config(void)
{
    if (!g_framework_ctx.initialized) {
        return NULL;
    }
    
    return &g_framework_ctx.config;
}

/**
 * @brief 框架运行时检查
 */
fw_framework_result_t fw_framework_runtime_check(void)
{
    if (!g_framework_ctx.initialized) {
        return FW_FRAMEWORK_ERROR;
    }
    
    /* 检查各个模块状态 */
    if (g_framework_ctx.config.enable_logger) {
        /* 检查日志系统 */
        if (fw_logger_get_pending_count() > FW_LOG_BUFFER_SIZE * 0.8) {
            FW_LOG_WARN(FW_FRAMEWORK_TAG, "日志缓冲区接近满载");
        }
    }
    
    if (g_framework_ctx.config.enable_error_handler) {
        /* 检查错误计数 */
        uint32_t error_count = fw_error_get_total_count();
        if (error_count > 100) {
            FW_LOG_WARN(FW_FRAMEWORK_TAG, "错误计数过高: %lu", error_count);
        }
    }
    
    return FW_FRAMEWORK_OK;
}

/**
 * @brief 获取框架状态信息
 */
fw_framework_result_t fw_framework_get_status_info(char* info_buffer, uint32_t buffer_size)
{
    uint32_t heap_free, heap_total;
    uint32_t uptime;
    
    if (info_buffer == NULL || buffer_size == 0) {
        return FW_FRAMEWORK_ERROR;
    }
    
    if (!g_framework_ctx.initialized) {
        snprintf(info_buffer, buffer_size, "框架未初始化");
        return FW_FRAMEWORK_ERROR;
    }
    
    /* 获取运行时间 */
    uptime = osKernelGetTickCount() - g_framework_ctx.init_timestamp;
    
    /* 获取内存信息 */
    fw_framework_get_memory_info(&heap_free, &heap_total);
    
    /* 格式化状态信息 */
    snprintf(info_buffer, buffer_size,
             "STM32G474嵌入式开发框架状态:\n"
             "版本: %s\n"
             "运行时间: %lu ms\n"
             "堆内存: %lu/%lu bytes (%.1f%% 使用)\n"
             "日志: %s\n"
             "错误处理: %s\n"
             "GPIO: %s\n"
             "RTOS: %s\n"
             "应用框架: %s\n",
             FW_FRAMEWORK_VERSION_STRING,
             uptime,
             heap_total - heap_free, heap_total,
             ((float)(heap_total - heap_free) / heap_total) * 100.0f,
             g_framework_ctx.config.enable_logger ? "启用" : "禁用",
             g_framework_ctx.config.enable_error_handler ? "启用" : "禁用",
             g_framework_ctx.config.enable_gpio ? "启用" : "禁用",
             g_framework_ctx.config.enable_rtos_wrapper ? "启用" : "禁用",
             g_framework_ctx.config.enable_app_framework ? "启用" : "禁用");
    
    return FW_FRAMEWORK_OK;
}

/**
 * @brief 设置框架日志级别
 */
fw_framework_result_t fw_framework_set_log_level(fw_log_level_t level)
{
    if (!g_framework_ctx.initialized) {
        return FW_FRAMEWORK_ERROR;
    }
    
    if (!g_framework_ctx.config.enable_logger) {
        return FW_FRAMEWORK_LOGGER_ERROR;
    }
    
    fw_logger_result_t result = fw_logger_set_level(level);
    if (result == FW_LOGGER_OK) {
        g_framework_ctx.config.log_level = level;
        return FW_FRAMEWORK_OK;
    }
    
    return FW_FRAMEWORK_LOGGER_ERROR;
}

/**
 * @brief 获取框架内存使用情况
 */
fw_framework_result_t fw_framework_get_memory_info(uint32_t* heap_free, uint32_t* heap_total)
{
    if (heap_free == NULL || heap_total == NULL) {
        return FW_FRAMEWORK_ERROR;
    }
    
    if (g_framework_ctx.config.enable_rtos_wrapper) {
        *heap_free = fw_rtos_get_free_heap_size();
        *heap_total = FW_DEFAULT_HEAP_SIZE;
    } else {
        *heap_free = 0;
        *heap_total = 0;
    }
    
    return FW_FRAMEWORK_OK;
}

/* 私有函数 ------------------------------------------------------------------*/

/**
 * @brief 初始化框架配置
 */
static fw_framework_result_t fw_framework_init_config(const fw_framework_config_t* config)
{
    if (config != NULL) {
        memcpy(&g_framework_ctx.config, config, sizeof(fw_framework_config_t));
    } else {
        /* 使用默认配置 */
        g_framework_ctx.config = (fw_framework_config_t)FW_FRAMEWORK_CONFIG_DEFAULT();
    }
    
    return FW_FRAMEWORK_OK;
}

/**
 * @brief 初始化日志系统
 */
static fw_framework_result_t fw_framework_init_logger(void)
{
    fw_logger_result_t result;
    
    result = fw_logger_init();
    if (result != FW_LOGGER_OK) {
        return FW_FRAMEWORK_LOGGER_ERROR;
    }
    
    /* 设置日志级别 */
    fw_logger_set_level(g_framework_ctx.config.log_level);
    
    /* 设置输出回调 */
    if (g_framework_ctx.config.log_output_callback != NULL) {
        fw_logger_set_output_callback(g_framework_ctx.config.log_output_callback);
    }
    
    return FW_FRAMEWORK_OK;
}

/**
 * @brief 初始化错误处理系统
 */
static fw_framework_result_t fw_framework_init_error_handler(void)
{
    fw_error_handler_result_t result;
    
    result = fw_error_handler_init();
    if (result != FW_ERROR_HANDLER_OK) {
        return FW_FRAMEWORK_ERROR_HANDLER_ERROR;
    }
    
    /* 设置错误回调 */
    if (g_framework_ctx.config.error_callback != NULL) {
        fw_error_handler_set_callback(g_framework_ctx.config.error_callback);
    }
    
    return FW_FRAMEWORK_OK;
}

/**
 * @brief 初始化硬件抽象层
 */
static fw_framework_result_t fw_framework_init_hardware(void)
{
    fw_gpio_result_t gpio_result;
    
    /* 初始化GPIO */
    if (g_framework_ctx.config.enable_gpio) {
        gpio_result = fw_gpio_init();
        if (gpio_result != FW_GPIO_OK) {
            return FW_FRAMEWORK_GPIO_ERROR;
        }
    }
    
    /* 其他硬件模块初始化可以在这里添加 */
    
    return FW_FRAMEWORK_OK;
}

/**
 * @brief 初始化RTOS封装层
 */
static fw_framework_result_t fw_framework_init_rtos(void)
{
    fw_rtos_result_t result;
    
    result = fw_rtos_init();
    if (result != FW_RTOS_OK) {
        return FW_FRAMEWORK_RTOS_ERROR;
    }
    
    return FW_FRAMEWORK_OK;
}

/**
 * @brief 初始化应用层框架
 */
static fw_framework_result_t fw_framework_init_app(void)
{
    fw_app_result_t result;
    
    result = fw_app_init();
    if (result != FW_APP_OK) {
        return FW_FRAMEWORK_APP_ERROR;
    }
    
    return FW_FRAMEWORK_OK;
}
