/**
  ******************************************************************************
  * @file    fw_logger.c
  * @brief   Framework logging system implementation
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "fw_logger.h"
#include "cmsis_os.h"
#include <stdio.h>
#include <string.h>

/* Private typedef -----------------------------------------------------------*/

/**
 * @brief Logger context structure
 */
typedef struct {
    bool initialized;
    bool enabled;
    fw_log_level_t min_level;
    fw_log_output_callback_t output_callback;
    
    /* Circular buffer for log messages */
    fw_log_message_t buffer[FW_LOG_BUFFER_SIZE];
    uint32_t write_index;
    uint32_t read_index;
    uint32_t count;
    
    /* Mutex for thread safety */
    osMutexId_t mutex;
} fw_logger_context_t;

/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/**
 * @brief Global logger context
 */
static fw_logger_context_t g_logger_ctx = {0};

/**
 * @brief Mutex attributes
 */
static const osMutexAttr_t g_logger_mutex_attr = {
    .name = "LoggerMutex"
};

/* Private function prototypes -----------------------------------------------*/
static fw_logger_result_t fw_logger_add_to_buffer(const fw_log_message_t* message);
static fw_logger_result_t fw_logger_get_from_buffer(fw_log_message_t* message);
static uint32_t fw_logger_get_timestamp(void);
static void fw_logger_default_output(const fw_log_message_t* message);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief Initialize the logging system
 */
fw_logger_result_t fw_logger_init(void)
{
    if (g_logger_ctx.initialized) {
        return FW_LOGGER_OK;
    }
    
    /* Initialize context */
    memset(&g_logger_ctx, 0, sizeof(g_logger_ctx));
    
    /* Create mutex */
    g_logger_ctx.mutex = osMutexNew(&g_logger_mutex_attr);
    if (g_logger_ctx.mutex == NULL) {
        return FW_LOGGER_ERROR;
    }
    
    /* Set default configuration */
    g_logger_ctx.enabled = true;
    g_logger_ctx.min_level = FW_GET_LOG_LEVEL();
    g_logger_ctx.output_callback = fw_logger_default_output;
    g_logger_ctx.initialized = true;
    
    FW_LOG_INFO("LOGGER", "Logger initialized");
    
    return FW_LOGGER_OK;
}

/**
 * @brief Deinitialize the logging system
 */
fw_logger_result_t fw_logger_deinit(void)
{
    if (!g_logger_ctx.initialized) {
        return FW_LOGGER_OK;
    }
    
    /* Flush remaining messages */
    fw_logger_flush();
    
    /* Delete mutex */
    if (g_logger_ctx.mutex != NULL) {
        osMutexDelete(g_logger_ctx.mutex);
        g_logger_ctx.mutex = NULL;
    }
    
    g_logger_ctx.initialized = false;
    
    return FW_LOGGER_OK;
}

/**
 * @brief Log a message with specified level and tag
 */
fw_logger_result_t fw_log(fw_log_level_t level, const char* tag, const char* format, ...)
{
    va_list args;
    fw_logger_result_t result;
    
    va_start(args, format);
    result = fw_log_va(level, tag, format, args);
    va_end(args);
    
    return result;
}

/**
 * @brief Log a message with variable arguments list
 */
fw_logger_result_t fw_log_va(fw_log_level_t level, const char* tag, const char* format, va_list args)
{
    fw_log_message_t message;
    fw_logger_result_t result;
    
    /* Check if logger is initialized and enabled */
    if (!g_logger_ctx.initialized || !g_logger_ctx.enabled) {
        return FW_LOGGER_OK;
    }
    
    /* Check log level */
    if (level > g_logger_ctx.min_level) {
        return FW_LOGGER_OK;
    }
    
    /* Validate parameters */
    if (tag == NULL || format == NULL) {
        return FW_LOGGER_INVALID_PARAM;
    }
    
    /* Acquire mutex */
    if (osMutexAcquire(g_logger_ctx.mutex, osWaitForever) != osOK) {
        return FW_LOGGER_ERROR;
    }
    
    /* Prepare message */
    message.timestamp = fw_logger_get_timestamp();
    message.level = level;
    message.tag = tag;
    
    /* Format message */
    vsnprintf(message.message, sizeof(message.message), format, args);
    
    /* Add to buffer */
    result = fw_logger_add_to_buffer(&message);
    
    /* Release mutex */
    osMutexRelease(g_logger_ctx.mutex);
    
    /* Output immediately if callback is set */
    if (result == FW_LOGGER_OK && g_logger_ctx.output_callback != NULL) {
        g_logger_ctx.output_callback(&message);
    }
    
    return result;
}

/**
 * @brief Set log output callback function
 */
fw_logger_result_t fw_logger_set_output_callback(fw_log_output_callback_t callback)
{
    if (!g_logger_ctx.initialized) {
        return FW_LOGGER_ERROR;
    }
    
    g_logger_ctx.output_callback = callback;
    return FW_LOGGER_OK;
}

/**
 * @brief Enable/disable logging
 */
fw_logger_result_t fw_logger_set_enabled(bool enabled)
{
    if (!g_logger_ctx.initialized) {
        return FW_LOGGER_ERROR;
    }
    
    g_logger_ctx.enabled = enabled;
    return FW_LOGGER_OK;
}

/**
 * @brief Set minimum log level
 */
fw_logger_result_t fw_logger_set_level(fw_log_level_t level)
{
    if (!g_logger_ctx.initialized) {
        return FW_LOGGER_ERROR;
    }
    
    if (!FW_IS_VALID_LOG_LEVEL(level)) {
        return FW_LOGGER_INVALID_PARAM;
    }
    
    g_logger_ctx.min_level = level;
    return FW_LOGGER_OK;
}

/**
 * @brief Get current log level
 */
fw_log_level_t fw_logger_get_level(void)
{
    return g_logger_ctx.min_level;
}

/**
 * @brief Check if logging is enabled for specified level
 */
bool fw_logger_is_level_enabled(fw_log_level_t level)
{
    return (g_logger_ctx.initialized && g_logger_ctx.enabled && level <= g_logger_ctx.min_level);
}

/**
 * @brief Flush log buffer
 */
fw_logger_result_t fw_logger_flush(void)
{
    fw_log_message_t message;
    
    if (!g_logger_ctx.initialized || g_logger_ctx.output_callback == NULL) {
        return FW_LOGGER_ERROR;
    }
    
    /* Acquire mutex */
    if (osMutexAcquire(g_logger_ctx.mutex, osWaitForever) != osOK) {
        return FW_LOGGER_ERROR;
    }
    
    /* Output all messages in buffer */
    while (fw_logger_get_from_buffer(&message) == FW_LOGGER_OK) {
        g_logger_ctx.output_callback(&message);
    }
    
    /* Release mutex */
    osMutexRelease(g_logger_ctx.mutex);
    
    return FW_LOGGER_OK;
}

/**
 * @brief Get number of messages in log buffer
 */
uint32_t fw_logger_get_pending_count(void)
{
    return g_logger_ctx.count;
}

/**
 * @brief Clear log buffer
 */
fw_logger_result_t fw_logger_clear_buffer(void)
{
    if (!g_logger_ctx.initialized) {
        return FW_LOGGER_ERROR;
    }
    
    /* Acquire mutex */
    if (osMutexAcquire(g_logger_ctx.mutex, osWaitForever) != osOK) {
        return FW_LOGGER_ERROR;
    }
    
    /* Clear buffer */
    g_logger_ctx.write_index = 0;
    g_logger_ctx.read_index = 0;
    g_logger_ctx.count = 0;
    
    /* Release mutex */
    osMutexRelease(g_logger_ctx.mutex);
    
    return FW_LOGGER_OK;
}

/**
 * @brief Get log level string representation
 */
const char* fw_logger_get_level_string(fw_log_level_t level)
{
    switch (level) {
        case FW_LOG_LEVEL_ERROR: return FW_LOG_LEVEL_ERROR_STR;
        case FW_LOG_LEVEL_WARN:  return FW_LOG_LEVEL_WARN_STR;
        case FW_LOG_LEVEL_INFO:  return FW_LOG_LEVEL_INFO_STR;
        case FW_LOG_LEVEL_DEBUG: return FW_LOG_LEVEL_DEBUG_STR;
        default: return "UNKNOWN";
    }
}

/**
 * @brief Default UART output callback
 */
void fw_logger_uart_output(const fw_log_message_t* message)
{
    if (message == NULL) {
        return;
    }
    
    /* Format: [timestamp] LEVEL TAG: message */
    printf("[%lu] %s %s: %s\r\n", 
           message->timestamp,
           fw_logger_get_level_string(message->level),
           message->tag,
           message->message);
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Add message to circular buffer
 */
static fw_logger_result_t fw_logger_add_to_buffer(const fw_log_message_t* message)
{
    if (g_logger_ctx.count >= FW_LOG_BUFFER_SIZE) {
        return FW_LOGGER_BUFFER_FULL;
    }
    
    /* Copy message to buffer */
    memcpy(&g_logger_ctx.buffer[g_logger_ctx.write_index], message, sizeof(fw_log_message_t));
    
    /* Update write index */
    g_logger_ctx.write_index = (g_logger_ctx.write_index + 1) % FW_LOG_BUFFER_SIZE;
    g_logger_ctx.count++;
    
    return FW_LOGGER_OK;
}

/**
 * @brief Get message from circular buffer
 */
static fw_logger_result_t fw_logger_get_from_buffer(fw_log_message_t* message)
{
    if (g_logger_ctx.count == 0) {
        return FW_LOGGER_ERROR;
    }
    
    /* Copy message from buffer */
    memcpy(message, &g_logger_ctx.buffer[g_logger_ctx.read_index], sizeof(fw_log_message_t));
    
    /* Update read index */
    g_logger_ctx.read_index = (g_logger_ctx.read_index + 1) % FW_LOG_BUFFER_SIZE;
    g_logger_ctx.count--;
    
    return FW_LOGGER_OK;
}

/**
 * @brief Get current timestamp
 */
static uint32_t fw_logger_get_timestamp(void)
{
    return osKernelGetTickCount();
}

/**
 * @brief Default output function (does nothing)
 */
static void fw_logger_default_output(const fw_log_message_t* message)
{
    /* Default implementation does nothing */
    /* Users should set their own output callback */
    (void)message;
}
