/**
  ******************************************************************************
  * @file    fw_rtos.c
  * @brief   框架RTOS封装层实现
  ******************************************************************************
  */

/* 包含文件 ------------------------------------------------------------------*/
#include "fw_rtos.h"
#include "fw_logger.h"
#include <string.h>

/* 私有类型定义 --------------------------------------------------------------*/

/**
 * @brief RTOS上下文结构体
 */
typedef struct {
    bool initialized;
    uint32_t task_count;
    uint32_t queue_count;
    uint32_t semaphore_count;
    uint32_t mutex_count;
} fw_rtos_context_t;

/* 私有定义 ------------------------------------------------------------------*/
#define FW_RTOS_TAG                     "RTOS"

/* 私有宏 --------------------------------------------------------------------*/
/* 私有变量 ------------------------------------------------------------------*/

/**
 * @brief 全局RTOS上下文
 */
static fw_rtos_context_t g_rtos_ctx = {0};

/* 私有函数原型 --------------------------------------------------------------*/
static osStatus_t fw_rtos_convert_priority(fw_task_priority_t priority, osPriority_t* os_priority);
static fw_rtos_result_t fw_rtos_convert_status(osStatus_t status);

/* 导出函数 ------------------------------------------------------------------*/

/**
 * @brief 初始化RTOS封装层
 */
fw_rtos_result_t fw_rtos_init(void)
{
    if (g_rtos_ctx.initialized) {
        FW_LOG_WARN(FW_RTOS_TAG, "RTOS封装层已初始化");
        return FW_RTOS_ALREADY_INITIALIZED;
    }
    
    /* 初始化上下文 */
    memset(&g_rtos_ctx, 0, sizeof(g_rtos_ctx));
    g_rtos_ctx.initialized = true;
    
    FW_LOG_INFO(FW_RTOS_TAG, "RTOS封装层初始化完成");
    
    return FW_RTOS_OK;
}

/**
 * @brief 反初始化RTOS封装层
 */
fw_rtos_result_t fw_rtos_deinit(void)
{
    if (!g_rtos_ctx.initialized) {
        return FW_RTOS_OK;
    }
    
    g_rtos_ctx.initialized = false;
    
    FW_LOG_INFO(FW_RTOS_TAG, "RTOS封装层反初始化完成");
    
    return FW_RTOS_OK;
}

/**
 * @brief 创建任务
 */
fw_rtos_result_t fw_task_create(fw_task_handle_t* task_handle)
{
    osThreadAttr_t thread_attr = {0};
    osPriority_t os_priority;
    osStatus_t status;
    
    /* 参数验证 */
    if (task_handle == NULL) {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_RTOS_TAG, "任务句柄为空");
        return FW_RTOS_INVALID_PARAM;
    }
    
    if (!g_rtos_ctx.initialized) {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_RTOS_TAG, "RTOS未初始化");
        return FW_RTOS_NOT_INITIALIZED;
    }
    
    if (task_handle->initialized) {
        FW_LOG_WARN(FW_RTOS_TAG, "任务 %s 已创建", task_handle->config.name);
        return FW_RTOS_ALREADY_INITIALIZED;
    }
    
    /* 转换优先级 */
    status = fw_rtos_convert_priority(task_handle->config.priority, &os_priority);
    if (status != osOK) {
        FW_ERROR_REPORT(FW_ERROR_INVALID_PARAMETER, FW_RTOS_TAG, 
                       "无效的任务优先级: %d", task_handle->config.priority);
        return FW_RTOS_INVALID_PARAM;
    }
    
    /* 设置任务属性 */
    thread_attr.name = task_handle->config.name;
    thread_attr.priority = os_priority;
    thread_attr.stack_size = task_handle->config.stack_size * 4; /* 转换为字节 */
    
    /* 创建任务 */
    task_handle->handle = osThreadNew(task_handle->config.function, 
                                     task_handle->config.argument, 
                                     &thread_attr);
    
    if (task_handle->handle == NULL) {
        FW_ERROR_REPORT(FW_ERROR_SYSTEM_TASK_CREATION_FAILED, FW_RTOS_TAG, 
                       "创建任务失败: %s", task_handle->config.name);
        return FW_RTOS_ERROR;
    }
    
    task_handle->initialized = true;
    task_handle->state = FW_TASK_STATE_READY;
    g_rtos_ctx.task_count++;
    
    FW_LOG_INFO(FW_RTOS_TAG, "任务创建成功: %s (优先级: %d, 堆栈: %d)", 
               task_handle->config.name, task_handle->config.priority, 
               task_handle->config.stack_size);
    
    return FW_RTOS_OK;
}

/**
 * @brief 删除任务
 */
fw_rtos_result_t fw_task_delete(fw_task_handle_t* task_handle)
{
    osStatus_t status;
    
    /* 参数验证 */
    if (task_handle == NULL) {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_RTOS_TAG, "任务句柄为空");
        return FW_RTOS_INVALID_PARAM;
    }
    
    if (!task_handle->initialized) {
        return FW_RTOS_OK;
    }
    
    /* 删除任务 */
    status = osThreadTerminate(task_handle->handle);
    if (status != osOK) {
        FW_ERROR_REPORT(FW_ERROR_APP_OPERATION_FAILED, FW_RTOS_TAG, 
                       "删除任务失败: %s", task_handle->config.name);
        return fw_rtos_convert_status(status);
    }
    
    task_handle->initialized = false;
    task_handle->state = FW_TASK_STATE_DELETED;
    task_handle->handle = NULL;
    g_rtos_ctx.task_count--;
    
    FW_LOG_INFO(FW_RTOS_TAG, "任务删除成功: %s", task_handle->config.name);
    
    return FW_RTOS_OK;
}

/**
 * @brief 挂起任务
 */
fw_rtos_result_t fw_task_suspend(fw_task_handle_t* task_handle)
{
    osStatus_t status;
    
    /* 参数验证 */
    if (task_handle == NULL) {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_RTOS_TAG, "任务句柄为空");
        return FW_RTOS_INVALID_PARAM;
    }
    
    if (!task_handle->initialized) {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_RTOS_TAG, 
                       "任务 %s 未初始化", task_handle->config.name);
        return FW_RTOS_NOT_INITIALIZED;
    }
    
    /* 挂起任务 */
    status = osThreadSuspend(task_handle->handle);
    if (status != osOK) {
        FW_ERROR_REPORT(FW_ERROR_APP_OPERATION_FAILED, FW_RTOS_TAG, 
                       "挂起任务失败: %s", task_handle->config.name);
        return fw_rtos_convert_status(status);
    }
    
    task_handle->state = FW_TASK_STATE_SUSPENDED;
    
    FW_LOG_DEBUG(FW_RTOS_TAG, "任务挂起: %s", task_handle->config.name);
    
    return FW_RTOS_OK;
}

/**
 * @brief 恢复任务
 */
fw_rtos_result_t fw_task_resume(fw_task_handle_t* task_handle)
{
    osStatus_t status;
    
    /* 参数验证 */
    if (task_handle == NULL) {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_RTOS_TAG, "任务句柄为空");
        return FW_RTOS_INVALID_PARAM;
    }
    
    if (!task_handle->initialized) {
        FW_ERROR_REPORT(FW_ERROR_FW_NOT_INITIALIZED, FW_RTOS_TAG, 
                       "任务 %s 未初始化", task_handle->config.name);
        return FW_RTOS_NOT_INITIALIZED;
    }
    
    /* 恢复任务 */
    status = osThreadResume(task_handle->handle);
    if (status != osOK) {
        FW_ERROR_REPORT(FW_ERROR_APP_OPERATION_FAILED, FW_RTOS_TAG, 
                       "恢复任务失败: %s", task_handle->config.name);
        return fw_rtos_convert_status(status);
    }
    
    task_handle->state = FW_TASK_STATE_READY;
    
    FW_LOG_DEBUG(FW_RTOS_TAG, "任务恢复: %s", task_handle->config.name);
    
    return FW_RTOS_OK;
}

/**
 * @brief 任务延时
 */
fw_rtos_result_t fw_task_delay(uint32_t delay_ms)
{
    osStatus_t status;
    
    status = osDelay(delay_ms);
    if (status != osOK) {
        return fw_rtos_convert_status(status);
    }
    
    return FW_RTOS_OK;
}

/**
 * @brief 获取任务状态
 */
fw_rtos_result_t fw_task_get_state(fw_task_handle_t* task_handle, fw_task_state_t* state)
{
    osThreadState_t os_state;
    
    /* 参数验证 */
    if (task_handle == NULL || state == NULL) {
        FW_ERROR_REPORT(FW_ERROR_NULL_POINTER, FW_RTOS_TAG, "参数为空");
        return FW_RTOS_INVALID_PARAM;
    }
    
    if (!task_handle->initialized) {
        *state = FW_TASK_STATE_INACTIVE;
        return FW_RTOS_OK;
    }
    
    /* 获取任务状态 */
    os_state = osThreadGetState(task_handle->handle);
    
    /* 转换状态 */
    switch (os_state) {
        case osThreadInactive:
            *state = FW_TASK_STATE_INACTIVE;
            break;
        case osThreadReady:
            *state = FW_TASK_STATE_READY;
            break;
        case osThreadRunning:
            *state = FW_TASK_STATE_RUNNING;
            break;
        case osThreadBlocked:
            *state = FW_TASK_STATE_BLOCKED;
            break;
        case osThreadTerminated:
            *state = FW_TASK_STATE_DELETED;
            break;
        default:
            *state = FW_TASK_STATE_INACTIVE;
            break;
    }
    
    task_handle->state = *state;
    
    return FW_RTOS_OK;
}

/**
 * @brief 获取系统时钟节拍
 */
uint32_t fw_rtos_get_tick_count(void)
{
    return osKernelGetTickCount();
}

/**
 * @brief 获取空闲任务剩余堆栈大小
 */
uint32_t fw_rtos_get_free_heap_size(void)
{
    return xPortGetFreeHeapSize();
}

/* 私有函数 ------------------------------------------------------------------*/

/**
 * @brief 转换任务优先级
 */
static osStatus_t fw_rtos_convert_priority(fw_task_priority_t priority, osPriority_t* os_priority)
{
    if (os_priority == NULL) {
        return osErrorParameter;
    }
    
    switch (priority) {
        case FW_TASK_PRIORITY_IDLE:
            *os_priority = osPriorityIdle;
            break;
        case FW_TASK_PRIORITY_LOW:
            *os_priority = osPriorityLow;
            break;
        case FW_TASK_PRIORITY_BELOW_NORMAL:
            *os_priority = osPriorityBelowNormal;
            break;
        case FW_TASK_PRIORITY_NORMAL:
            *os_priority = osPriorityNormal;
            break;
        case FW_TASK_PRIORITY_ABOVE_NORMAL:
            *os_priority = osPriorityAboveNormal;
            break;
        case FW_TASK_PRIORITY_HIGH:
            *os_priority = osPriorityHigh;
            break;
        case FW_TASK_PRIORITY_REALTIME:
            *os_priority = osPriorityRealtime;
            break;
        default:
            return osErrorParameter;
    }
    
    return osOK;
}

/**
 * @brief 转换RTOS状态
 */
static fw_rtos_result_t fw_rtos_convert_status(osStatus_t status)
{
    switch (status) {
        case osOK:
            return FW_RTOS_OK;
        case osErrorTimeout:
            return FW_RTOS_TIMEOUT;
        case osErrorParameter:
            return FW_RTOS_INVALID_PARAM;
        case osErrorResource:
            return FW_RTOS_RESOURCE_NOT_AVAILABLE;
        default:
            return FW_RTOS_ERROR;
    }
}
