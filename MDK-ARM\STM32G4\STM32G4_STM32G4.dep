Dependencies for Project 'STM32G4', Target 'STM32G4': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (startup_stm32g474xx.s)(0x688ADDC3)(--cpu Cortex-M4.fp.sp -g --apcs=interwork -I ..\Core\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

--pd "__UVISION_VERSION SETA 534" --pd "_RTE_ SETA 1" --pd "STM32G474xx SETA 1" --pd "_RTE_ SETA 1"

--list startup_stm32g474xx.lst --xref -o stm32g4\startup_stm32g474xx.o --depend stm32g4\startup_stm32g474xx.d)
F (../Core/Src/main.c)(0x688B1CC5)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\main.o --omf_browse stm32g4\main.crf --depend stm32g4\main.d)
I (../Core/Inc/main.h)(0x688AE010)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x688AD196)
I (../Core/Inc/usart.h)(0x688ADDC2)
I (../Core/Inc/gpio.h)(0x688ADDC1)
I (..\Framework\Config\fw_framework.h)(0x688B0CDE)
I (..\Framework\Common\Inc\fw_config.h)(0x688B0B00)
I (D:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\Framework\Common\Inc\fw_logger.h)(0x688B0B39)
I (D:\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (..\Framework\Common\Inc\fw_error.h)(0x688B0B48)
I (..\Framework\HAL\Inc\fw_gpio.h)(0x688B0B82)
I (..\Framework\Middleware\Inc\fw_rtos.h)(0x688B0BB7)
I (..\Framework\Application\Inc\fw_app.h)(0x688B0C1B)
I (D:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (../Core/Src/gpio.c)(0x688AE010)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\gpio.o --omf_browse stm32g4\gpio.crf --depend stm32g4\gpio.d)
I (../Core/Inc/gpio.h)(0x688ADDC1)
I (../Core/Inc/main.h)(0x688AE010)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Core/Src/app_freertos.c)(0x688ADDC2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\app_freertos.o --omf_browse stm32g4\app_freertos.crf --depend stm32g4\app_freertos.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
I (../Core/Inc/main.h)(0x688AE010)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x688AD196)
F (../Core/Src/usart.c)(0x688ADDC2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\usart.o --omf_browse stm32g4\usart.crf --depend stm32g4\usart.d)
I (../Core/Inc/usart.h)(0x688ADDC2)
I (../Core/Inc/main.h)(0x688AE010)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Core/Src/stm32g4xx_it.c)(0x688ADDC2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_it.o --omf_browse stm32g4\stm32g4xx_it.crf --depend stm32g4\stm32g4xx_it.d)
I (../Core/Inc/main.h)(0x688AE010)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_it.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
F (../Core/Src/stm32g4xx_hal_msp.c)(0x688ADDC2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_msp.o --omf_browse stm32g4\stm32g4xx_hal_msp.crf --depend stm32g4\stm32g4xx_hal_msp.d)
I (../Core/Inc/main.h)(0x688AE010)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal.o --omf_browse stm32g4\stm32g4xx_hal.crf --depend stm32g4\stm32g4xx_hal.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_rcc.o --omf_browse stm32g4\stm32g4xx_hal_rcc.crf --depend stm32g4\stm32g4xx_hal_rcc.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_rcc_ex.o --omf_browse stm32g4\stm32g4xx_hal_rcc_ex.crf --depend stm32g4\stm32g4xx_hal_rcc_ex.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_flash.o --omf_browse stm32g4\stm32g4xx_hal_flash.crf --depend stm32g4\stm32g4xx_hal_flash.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_flash_ex.o --omf_browse stm32g4\stm32g4xx_hal_flash_ex.crf --depend stm32g4\stm32g4xx_hal_flash_ex.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_flash_ramfunc.o --omf_browse stm32g4\stm32g4xx_hal_flash_ramfunc.crf --depend stm32g4\stm32g4xx_hal_flash_ramfunc.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_gpio.o --omf_browse stm32g4\stm32g4xx_hal_gpio.crf --depend stm32g4\stm32g4xx_hal_gpio.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_exti.o --omf_browse stm32g4\stm32g4xx_hal_exti.crf --depend stm32g4\stm32g4xx_hal_exti.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_dma.o --omf_browse stm32g4\stm32g4xx_hal_dma.crf --depend stm32g4\stm32g4xx_hal_dma.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_dma_ex.o --omf_browse stm32g4\stm32g4xx_hal_dma_ex.crf --depend stm32g4\stm32g4xx_hal_dma_ex.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_pwr.o --omf_browse stm32g4\stm32g4xx_hal_pwr.crf --depend stm32g4\stm32g4xx_hal_pwr.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_pwr_ex.o --omf_browse stm32g4\stm32g4xx_hal_pwr_ex.crf --depend stm32g4\stm32g4xx_hal_pwr_ex.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_cortex.o --omf_browse stm32g4\stm32g4xx_hal_cortex.crf --depend stm32g4\stm32g4xx_hal_cortex.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_tim.o --omf_browse stm32g4\stm32g4xx_hal_tim.crf --depend stm32g4\stm32g4xx_hal_tim.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_tim_ex.o --omf_browse stm32g4\stm32g4xx_hal_tim_ex.crf --depend stm32g4\stm32g4xx_hal_tim_ex.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_uart.o --omf_browse stm32g4\stm32g4xx_hal_uart.crf --depend stm32g4\stm32g4xx_hal_uart.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stm32g4xx_hal_uart_ex.o --omf_browse stm32g4\stm32g4xx_hal_uart_ex.crf --depend stm32g4\stm32g4xx_hal_uart_ex.d)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Core/Src/system_stm32g4xx.c)(0x688AD194)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\system_stm32g4xx.o --omf_browse stm32g4\system_stm32g4xx.crf --depend stm32g4\system_stm32g4xx.d)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/croutine.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\croutine.o --omf_browse stm32g4\croutine.crf --depend stm32g4\croutine.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/croutine.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\event_groups.o --omf_browse stm32g4\event_groups.crf --depend stm32g4\event_groups.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/list.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\list.o --omf_browse stm32g4\list.crf --depend stm32g4\list.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/queue.c)(0x688AD197)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\queue.o --omf_browse stm32g4\queue.crf --depend stm32g4\queue.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c)(0x688AD197)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\stream_buffer.o --omf_browse stm32g4\stream_buffer.crf --depend stm32g4\stream_buffer.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/stream_buffer.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/tasks.c)(0x688AD197)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\tasks.o --omf_browse stm32g4\tasks.crf --depend stm32g4\tasks.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/stack_macros.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/timers.c)(0x688AD197)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\timers.o --omf_browse stm32g4\timers.crf --depend stm32g4\timers.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c)(0x688AD196)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\cmsis_os2.o --omf_browse stm32g4\cmsis_os2.crf --depend stm32g4\cmsis_os2.d)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/semphr.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/freertos_mpool.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/freertos_os2.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h)(0x688AD194)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g474xx.h)(0x688AD194)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x688AD196)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x688AD196)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x688AD196)
I (../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h)(0x688AD194)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h)(0x688AD196)
I (../Core/Inc/stm32g4xx_hal_conf.h)(0x688ADDC2)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h)(0x688AD196)
I (../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c)(0x688AD197)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\heap_4.o --omf_browse stm32g4\heap_4.crf --depend stm32g4\heap_4.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
F (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/port.c)(0x688AD197)(--c99 -c --cpu Cortex-M4.fp.sp -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc -I ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ..\Framework\Config -I ..\Framework\Middleware\Inc -I ..\Framework\Common\Inc -I ..\Framework\HAL\Inc -I ..\Framework\Application\Inc

-I.\RTE\_STM32G4

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="534" -D_RTE_ -DSTM32G474xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G474xx

-o stm32g4\port.o --omf_browse stm32g4\port.crf --depend stm32g4\port.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x688AD196)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (../Core/Inc/FreeRTOSConfig.h)(0x688ADDC2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x688AD197)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x688AD196)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x688AD196)
