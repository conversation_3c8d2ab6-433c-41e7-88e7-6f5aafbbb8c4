<html>
<body>
<pre>
<h1>�Vision Build Log</h1>
<h2>Tool Versions:</h2>
IDE-Version: ��Vision V5.34.0.0
Copyright (C) 2021 ARM Ltd and ARM Germany GmbH. All rights reserved.
License Information: 1 <EMAIL>, 1, LIC=CZLZX-HL8TD-Y89FK-ZQ5QH-XARN7-RJDR0
 
Tool Versions:
Toolchain:       MDK-ARM Plus  Version: 5.34.0.0
Toolchain Path:  D:\Keil_v5\ARM\ARMCC\Bin
C Compiler:      Armcc.exe V5.06 update 7 (build 960)
Assembler:       Armasm.exe V5.06 update 7 (build 960)
Linker/Locator:  ArmLink.exe V5.06 update 7 (build 960)
Library Manager: ArmAr.exe V5.06 update 7 (build 960)
Hex Converter:   FromElf.exe V5.06 update 7 (build 960)
CPU DLL:         SARMCM3.DLL V5.34.0.0
Dialog DLL:      DCM.DLL V1.17.3.0
Target DLL:      STLink\ST-LINKIII-KEIL_SWO.dll V3.0.8.0
Dialog DLL:      TCM.DLL V1.48.0.0
 
<h2>Project:</h2>
D:\workspace\STM32G474xC Temp\STM32G4\MDK-ARM\STM32G4.uvprojx
Project File Date:  07/31/2025

<h2>Output:</h2>
*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\Keil_v5\ARM\ARMCC\Bin'
Build target 'STM32G4'
compiling main.c...
linking...
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_error_default_handler (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_error_get_count_by_severity (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_error_get_total_count (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_error_report (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_framework_get_memory_info (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_framework_get_status_info (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_framework_get_version (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_framework_init (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_framework_runtime_check (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_gpio_pin_init (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_gpio_pin_toggle (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_gpio_pin_write (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_log (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_logger_get_level_string (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_rtos_get_tick_count (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_task_create (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_task_delay (referred from main.o).
STM32G4\STM32G4.axf: Error: L6218E: Undefined symbol fw_task_get_state (referred from main.o).
Not enough information to list image symbols.
Not enough information to list load addresses in the image map.
Finished: 2 information, 0 warning and 18 error messages.
"STM32G4\STM32G4.axf" - 18 Error(s), 0 Warning(s).

<h2>Software Packages used:</h2>

Package Vendor: ARM
                http://www.keil.com/pack/ARM.CMSIS.5.7.0.pack
                ARM.CMSIS.5.7.0
                CMSIS (Cortex Microcontroller Software Interface Standard)
   * Component: CORE Version: 5.4.0

Package Vendor: Keil
                https://www.keil.com/pack/Keil.STM32G4xx_DFP.2.0.0.pack
                Keil.STM32G4xx_DFP.2.0.0
                STMicroelectronics STM32G4 Series Device Support

<h2>Collection of Component include folders:</h2>
  .\RTE\_STM32G4
  C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

<h2>Collection of Component Files used:</h2>

   * Component: ARM::CMSIS:CORE:5.4.0
      Include file:  CMSIS\Core\Include\tz_context.h
Target not created.
Build Time Elapsed:  00:00:02
</pre>
</body>
</html>
