<html>
<body>
<pre>
<h1>�Vision Build Log</h1>
<h2>Tool Versions:</h2>
IDE-Version: ��Vision V5.34.0.0
Copyright (C) 2021 ARM Ltd and ARM Germany GmbH. All rights reserved.
License Information: 1 <EMAIL>, 1, LIC=CZLZX-HL8TD-Y89FK-ZQ5QH-XARN7-RJDR0
 
Tool Versions:
Toolchain:       MDK-ARM Plus  Version: 5.34.0.0
Toolchain Path:  D:\Keil_v5\ARM\ARMCC\Bin
C Compiler:      Armcc.exe V5.06 update 7 (build 960)
Assembler:       Armasm.exe V5.06 update 7 (build 960)
Linker/Locator:  ArmLink.exe V5.06 update 7 (build 960)
Library Manager: ArmAr.exe V5.06 update 7 (build 960)
Hex Converter:   FromElf.exe V5.06 update 7 (build 960)
CPU DLL:         SARMCM3.DLL V5.34.0.0
Dialog DLL:      DCM.DLL V1.17.3.0
Target DLL:      STLink\ST-LINKIII-KEIL_SWO.dll V3.0.8.0
Dialog DLL:      TCM.DLL V1.48.0.0
 
<h2>Project:</h2>
D:\workspace\STM32G474xC Temp\STM32G4\MDK-ARM\STM32G4.uvprojx
Project File Date:  07/31/2025

<h2>Output:</h2>
*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\Keil_v5\ARM\ARMCC\Bin'
Build target 'STM32G4'
compiling main.c...
../Core/Src/main.c(233): warning:  #870-D: invalid multibyte character sequence
    printf("系统启动�?...\r\n");
../Core/Src/main.c(244): warning:  #870-D: invalid multibyte character sequence
    printf("正在初始化框�?...\r\n");
../Core/Src/main.c(264): warning:  #870-D: invalid multibyte character sequence
      printf("错误: 框架初始化失�? (错误代码: %d)\r\n", result);
../Core/Src/main.c(269): warning:  #870-D: invalid multibyte character sequence
    printf("框架初始化成�?!\r\n");
../Core/Src/main.c(579): error: At end of source:  #18: expected a ")"
../Core/Src/main.c(273): error:  #274: improperly terminated macro invocation
    FW_LOG_INFO("MAIN", "框架初始化完成，开始系统测�?");
../Core/Src/main.c(579): error: At end of source:  #67: expected a "}"
../Core/Src/main.c(54): warning:  #177-D: variable "g_led_handle"  was declared but never referenced
  static fw_gpio_handle_t g_led_handle;
../Core/Src/main.c(55): warning:  #177-D: variable "g_led_task_handle"  was declared but never referenced
  static fw_task_handle_t g_led_task_handle;
../Core/Src/main.c(56): warning:  #177-D: variable "g_demo_task_handle"  was declared but never referenced
  static fw_task_handle_t g_demo_task_handle;
../Core/Src/main.c(59): warning:  #550-D: variable "g_framework_initialized"  was set but never used
  static bool g_framework_initialized = false;
../Core/Src/main.c(60): warning:  #177-D: variable "g_demo_counter"  was declared but never referenced
  static uint32_t g_demo_counter = 0;
../Core/Src/main.c(69): error:  #114: function "LED_Application_Init"  was referenced but not defined
  static void LED_Application_Init(void);
../Core/Src/main.c(73): warning:  #177-D: function "LED_Task_Function"  was declared but never referenced
  static void LED_Task_Function(void *argument);
../Core/Src/main.c(74): warning:  #177-D: function "Demo_Task_Function"  was declared but never referenced
  static void Demo_Task_Function(void *argument);
../Core/Src/main.c(77): error:  #114: function "Print_Framework_Status"  was referenced but not defined
  static void Print_Framework_Status(void);
../Core/Src/main.c(78): error:  #114: function "Print_System_Info"  was referenced but not defined
  static void Print_System_Info(void);
../Core/Src/main.c: 11 warnings, 6 errors
"STM32G4\STM32G4.axf" - 6 Error(s), 11 Warning(s).

<h2>Software Packages used:</h2>

Package Vendor: ARM
                http://www.keil.com/pack/ARM.CMSIS.5.7.0.pack
                ARM.CMSIS.5.7.0
                CMSIS (Cortex Microcontroller Software Interface Standard)
   * Component: CORE Version: 5.4.0

Package Vendor: Keil
                https://www.keil.com/pack/Keil.STM32G4xx_DFP.2.0.0.pack
                Keil.STM32G4xx_DFP.2.0.0
                STMicroelectronics STM32G4 Series Device Support

<h2>Collection of Component include folders:</h2>
  .\RTE\_STM32G4
  C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

<h2>Collection of Component Files used:</h2>

   * Component: ARM::CMSIS:CORE:5.4.0
      Include file:  CMSIS\Core\Include\tz_context.h
Target not created.
Build Time Elapsed:  00:00:01
</pre>
</body>
</html>
