/**
  ******************************************************************************
  * @file    fw_error.c
  * @brief   Framework error handling system implementation
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "fw_error.h"
#include "fw_logger.h"
#include "cmsis_os.h"
#include <stdio.h>
#include <string.h>
#include <stdarg.h>

/* Private typedef -----------------------------------------------------------*/

/**
 * @brief Error handler context structure
 */
typedef struct {
    bool initialized;
    fw_error_handler_callback_t callback;
    
    /* Error history */
    fw_error_info_t history[FW_ERROR_HISTORY_SIZE];
    uint32_t history_index;
    uint32_t total_count;
    uint32_t count_by_severity[4];  /* Index corresponds to fw_error_severity_t */
    
    /* Mutex for thread safety */
    osMutexId_t mutex;
} fw_error_handler_context_t;

/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/**
 * @brief Global error handler context
 */
static fw_error_handler_context_t g_error_ctx = {0};

/**
 * @brief Mutex attributes
 */
static const osMutexAttr_t g_error_mutex_attr = {
    .name = "ErrorMutex"
};

/* Private function prototypes -----------------------------------------------*/
static void fw_error_add_to_history(const fw_error_info_t* error_info);
static uint32_t fw_error_get_timestamp(void);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief Initialize error handling system
 */
fw_error_handler_result_t fw_error_handler_init(void)
{
    if (g_error_ctx.initialized) {
        return FW_ERROR_HANDLER_OK;
    }
    
    /* Initialize context */
    memset(&g_error_ctx, 0, sizeof(g_error_ctx));
    
    /* Create mutex */
    g_error_ctx.mutex = osMutexNew(&g_error_mutex_attr);
    if (g_error_ctx.mutex == NULL) {
        return FW_ERROR_HANDLER_ERROR;
    }
    
    /* Set default callback */
    g_error_ctx.callback = fw_error_default_handler;
    g_error_ctx.initialized = true;
    
    FW_LOG_INFO("ERROR", "Error handler initialized");
    
    return FW_ERROR_HANDLER_OK;
}

/**
 * @brief Deinitialize error handling system
 */
fw_error_handler_result_t fw_error_handler_deinit(void)
{
    if (!g_error_ctx.initialized) {
        return FW_ERROR_HANDLER_OK;
    }
    
    /* Delete mutex */
    if (g_error_ctx.mutex != NULL) {
        osMutexDelete(g_error_ctx.mutex);
        g_error_ctx.mutex = NULL;
    }
    
    g_error_ctx.initialized = false;
    
    return FW_ERROR_HANDLER_OK;
}

/**
 * @brief Report an error
 */
fw_error_handler_result_t fw_error_report(fw_error_code_t code, 
                                         fw_error_severity_t severity,
                                         const char* module,
                                         const char* file,
                                         uint32_t line,
                                         const char* function,
                                         const char* format, ...)
{
    fw_error_info_t error_info;
    va_list args;
    
    /* Check if error handler is initialized */
    if (!g_error_ctx.initialized) {
        return FW_ERROR_HANDLER_ERROR;
    }
    
    /* Validate parameters */
    if (module == NULL || file == NULL || function == NULL || format == NULL) {
        return FW_ERROR_HANDLER_INVALID_PARAM;
    }
    
    /* Acquire mutex */
    if (osMutexAcquire(g_error_ctx.mutex, osWaitForever) != osOK) {
        return FW_ERROR_HANDLER_ERROR;
    }
    
    /* Prepare error information */
    error_info.code = code;
    error_info.severity = severity;
    error_info.timestamp = fw_error_get_timestamp();
    error_info.file = file;
    error_info.line = line;
    error_info.function = function;
    error_info.module = module;
    
    /* Format description */
    va_start(args, format);
    vsnprintf(error_info.description, sizeof(error_info.description), format, args);
    va_end(args);
    
    /* Add to history */
    fw_error_add_to_history(&error_info);
    
    /* Update counters */
    g_error_ctx.total_count++;
    if (severity < 4) {
        g_error_ctx.count_by_severity[severity]++;
    }
    
    /* Release mutex */
    osMutexRelease(g_error_ctx.mutex);
    
    /* Call error handler callback */
    if (g_error_ctx.callback != NULL) {
        g_error_ctx.callback(&error_info);
    }
    
    return FW_ERROR_HANDLER_OK;
}

/**
 * @brief Set error handler callback
 */
fw_error_handler_result_t fw_error_handler_set_callback(fw_error_handler_callback_t callback)
{
    if (!g_error_ctx.initialized) {
        return FW_ERROR_HANDLER_ERROR;
    }
    
    g_error_ctx.callback = callback;
    return FW_ERROR_HANDLER_OK;
}

/**
 * @brief Get error code string representation
 */
const char* fw_error_get_code_string(fw_error_code_t code)
{
    switch (code) {
        case FW_ERROR_NONE: return "NO_ERROR";
        case FW_ERROR_SYSTEM_INIT_FAILED: return "SYSTEM_INIT_FAILED";
        case FW_ERROR_SYSTEM_CONFIG_INVALID: return "SYSTEM_CONFIG_INVALID";
        case FW_ERROR_SYSTEM_MEMORY_ALLOCATION_FAILED: return "MEMORY_ALLOCATION_FAILED";
        case FW_ERROR_SYSTEM_TASK_CREATION_FAILED: return "TASK_CREATION_FAILED";
        case FW_ERROR_HW_GPIO_INIT_FAILED: return "GPIO_INIT_FAILED";
        case FW_ERROR_HW_UART_INIT_FAILED: return "UART_INIT_FAILED";
        case FW_ERROR_HW_TIMER_INIT_FAILED: return "TIMER_INIT_FAILED";
        case FW_ERROR_HW_ADC_INIT_FAILED: return "ADC_INIT_FAILED";
        case FW_ERROR_HW_PERIPHERAL_NOT_READY: return "PERIPHERAL_NOT_READY";
        case FW_ERROR_COMM_TIMEOUT: return "COMM_TIMEOUT";
        case FW_ERROR_COMM_BUFFER_OVERFLOW: return "COMM_BUFFER_OVERFLOW";
        case FW_ERROR_COMM_INVALID_DATA: return "COMM_INVALID_DATA";
        case FW_ERROR_COMM_CONNECTION_LOST: return "COMM_CONNECTION_LOST";
        case FW_ERROR_APP_INVALID_PARAMETER: return "APP_INVALID_PARAMETER";
        case FW_ERROR_APP_INVALID_STATE: return "APP_INVALID_STATE";
        case FW_ERROR_APP_OPERATION_FAILED: return "APP_OPERATION_FAILED";
        case FW_ERROR_APP_RESOURCE_BUSY: return "APP_RESOURCE_BUSY";
        case FW_ERROR_FW_NOT_INITIALIZED: return "FW_NOT_INITIALIZED";
        case FW_ERROR_FW_ALREADY_INITIALIZED: return "FW_ALREADY_INITIALIZED";
        case FW_ERROR_FW_INVALID_CONFIGURATION: return "FW_INVALID_CONFIGURATION";
        case FW_ERROR_FW_OPERATION_NOT_SUPPORTED: return "FW_OPERATION_NOT_SUPPORTED";
        case FW_ERROR_INVALID_PARAMETER: return "INVALID_PARAMETER";
        case FW_ERROR_NULL_POINTER: return "NULL_POINTER";
        case FW_ERROR_BUFFER_TOO_SMALL: return "BUFFER_TOO_SMALL";
        case FW_ERROR_OPERATION_TIMEOUT: return "OPERATION_TIMEOUT";
        case FW_ERROR_RESOURCE_NOT_AVAILABLE: return "RESOURCE_NOT_AVAILABLE";
        default: return "UNKNOWN_ERROR";
    }
}

/**
 * @brief Get error severity string representation
 */
const char* fw_error_get_severity_string(fw_error_severity_t severity)
{
    switch (severity) {
        case FW_ERROR_SEVERITY_INFO: return "INFO";
        case FW_ERROR_SEVERITY_WARNING: return "WARNING";
        case FW_ERROR_SEVERITY_ERROR: return "ERROR";
        case FW_ERROR_SEVERITY_CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

/**
 * @brief Get last error information
 */
const fw_error_info_t* fw_error_get_last_error(void)
{
    if (!g_error_ctx.initialized || g_error_ctx.total_count == 0) {
        return NULL;
    }
    
    uint32_t last_index = (g_error_ctx.history_index + FW_ERROR_HISTORY_SIZE - 1) % FW_ERROR_HISTORY_SIZE;
    return &g_error_ctx.history[last_index];
}

/**
 * @brief Get error count by severity
 */
uint32_t fw_error_get_count_by_severity(fw_error_severity_t severity)
{
    if (!g_error_ctx.initialized || severity >= 4) {
        return 0;
    }
    
    return g_error_ctx.count_by_severity[severity];
}

/**
 * @brief Get total error count
 */
uint32_t fw_error_get_total_count(void)
{
    return g_error_ctx.total_count;
}

/**
 * @brief Clear error history
 */
fw_error_handler_result_t fw_error_clear_history(void)
{
    if (!g_error_ctx.initialized) {
        return FW_ERROR_HANDLER_ERROR;
    }
    
    /* Acquire mutex */
    if (osMutexAcquire(g_error_ctx.mutex, osWaitForever) != osOK) {
        return FW_ERROR_HANDLER_ERROR;
    }
    
    /* Clear history and counters */
    memset(g_error_ctx.history, 0, sizeof(g_error_ctx.history));
    memset(g_error_ctx.count_by_severity, 0, sizeof(g_error_ctx.count_by_severity));
    g_error_ctx.history_index = 0;
    g_error_ctx.total_count = 0;
    
    /* Release mutex */
    osMutexRelease(g_error_ctx.mutex);
    
    return FW_ERROR_HANDLER_OK;
}

/**
 * @brief Get error history
 */
fw_error_handler_result_t fw_error_get_history(fw_error_info_t* history_buffer, 
                                              uint32_t buffer_size, 
                                              uint32_t* count)
{
    if (!g_error_ctx.initialized || history_buffer == NULL || count == NULL) {
        return FW_ERROR_HANDLER_INVALID_PARAM;
    }
    
    /* Acquire mutex */
    if (osMutexAcquire(g_error_ctx.mutex, osWaitForever) != osOK) {
        return FW_ERROR_HANDLER_ERROR;
    }
    
    /* Calculate number of errors to copy */
    uint32_t errors_in_history = (g_error_ctx.total_count < FW_ERROR_HISTORY_SIZE) ? 
                                  g_error_ctx.total_count : FW_ERROR_HISTORY_SIZE;
    uint32_t errors_to_copy = (buffer_size < errors_in_history) ? buffer_size : errors_in_history;
    
    /* Copy errors from history */
    for (uint32_t i = 0; i < errors_to_copy; i++) {
        uint32_t index = (g_error_ctx.history_index + FW_ERROR_HISTORY_SIZE - errors_to_copy + i) % FW_ERROR_HISTORY_SIZE;
        memcpy(&history_buffer[i], &g_error_ctx.history[index], sizeof(fw_error_info_t));
    }
    
    *count = errors_to_copy;
    
    /* Release mutex */
    osMutexRelease(g_error_ctx.mutex);
    
    return FW_ERROR_HANDLER_OK;
}

/**
 * @brief Default error handler callback
 */
void fw_error_default_handler(const fw_error_info_t* error_info)
{
    if (error_info == NULL) {
        return;
    }
    
    /* Log error using framework logger */
    fw_log_level_t log_level;
    switch (error_info->severity) {
        case FW_ERROR_SEVERITY_INFO:     log_level = FW_LOG_LEVEL_INFO; break;
        case FW_ERROR_SEVERITY_WARNING:  log_level = FW_LOG_LEVEL_WARN; break;
        case FW_ERROR_SEVERITY_ERROR:    log_level = FW_LOG_LEVEL_ERROR; break;
        case FW_ERROR_SEVERITY_CRITICAL: log_level = FW_LOG_LEVEL_ERROR; break;
        default:                         log_level = FW_LOG_LEVEL_ERROR; break;
    }
    
    fw_log(log_level, "ERROR", "[%s] %s in %s:%lu (%s) - %s",
           fw_error_get_severity_string(error_info->severity),
           fw_error_get_code_string(error_info->code),
           error_info->file,
           error_info->line,
           error_info->function,
           error_info->description);
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Add error to history (circular buffer)
 */
static void fw_error_add_to_history(const fw_error_info_t* error_info)
{
    memcpy(&g_error_ctx.history[g_error_ctx.history_index], error_info, sizeof(fw_error_info_t));
    g_error_ctx.history_index = (g_error_ctx.history_index + 1) % FW_ERROR_HISTORY_SIZE;
}

/**
 * @brief Get current timestamp
 */
static uint32_t fw_error_get_timestamp(void)
{
    return osKernelGetTickCount();
}
