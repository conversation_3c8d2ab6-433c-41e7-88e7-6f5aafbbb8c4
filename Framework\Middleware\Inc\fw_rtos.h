/**
  ******************************************************************************
  * @file    fw_rtos.h
  * @brief   框架RTOS封装层头文件
  ******************************************************************************
  * @attention
  *
  * 此文件包含STM32G474嵌入式开发框架的FreeRTOS封装层定义和函数。
  *
  ******************************************************************************
  */

#ifndef __FW_RTOS_H__
#define __FW_RTOS_H__

#ifdef __cplusplus
extern "C" {
#endif

/* 包含文件 ------------------------------------------------------------------*/
#include "fw_config.h"
#include "fw_error.h"
#include "cmsis_os.h"
#include <stdint.h>
#include <stdbool.h>

/* 导出类型 ------------------------------------------------------------------*/

/**
 * @brief 任务优先级枚举
 */
typedef enum {
    FW_TASK_PRIORITY_IDLE = 1,
    FW_TASK_PRIORITY_LOW = 8,
    FW_TASK_PRIORITY_BELOW_NORMAL = 16,
    FW_TASK_PRIORITY_NORMAL = 24,
    FW_TASK_PRIORITY_ABOVE_NORMAL = 32,
    FW_TASK_PRIORITY_HIGH = 40,
    FW_TASK_PRIORITY_REALTIME = 48
} fw_task_priority_t;

/**
 * @brief 任务状态枚举
 */
typedef enum {
    FW_TASK_STATE_INACTIVE = 0,
    FW_TASK_STATE_READY,
    FW_TASK_STATE_RUNNING,
    FW_TASK_STATE_BLOCKED,
    FW_TASK_STATE_SUSPENDED,
    FW_TASK_STATE_DELETED
} fw_task_state_t;

/**
 * @brief 任务函数类型定义
 */
typedef void (*fw_task_function_t)(void* argument);

/**
 * @brief 任务配置结构体
 */
typedef struct {
    const char* name;
    fw_task_function_t function;
    fw_task_priority_t priority;
    uint32_t stack_size;
    void* argument;
} fw_task_config_t;

/**
 * @brief 任务句柄结构体
 */
typedef struct {
    osThreadId_t handle;
    fw_task_config_t config;
    bool initialized;
    fw_task_state_t state;
} fw_task_handle_t;

/**
 * @brief 消息队列句柄结构体
 */
typedef struct {
    osMessageQueueId_t handle;
    const char* name;
    uint32_t msg_count;
    uint32_t msg_size;
    bool initialized;
} fw_queue_handle_t;

/**
 * @brief 信号量句柄结构体
 */
typedef struct {
    osSemaphoreId_t handle;
    const char* name;
    uint32_t max_count;
    uint32_t initial_count;
    bool initialized;
} fw_semaphore_handle_t;

/**
 * @brief 互斥量句柄结构体
 */
typedef struct {
    osMutexId_t handle;
    const char* name;
    bool initialized;
} fw_mutex_handle_t;

/**
 * @brief RTOS结果枚举
 */
typedef enum {
    FW_RTOS_OK = 0,
    FW_RTOS_ERROR,
    FW_RTOS_TIMEOUT,
    FW_RTOS_INVALID_PARAM,
    FW_RTOS_NOT_INITIALIZED,
    FW_RTOS_ALREADY_INITIALIZED,
    FW_RTOS_RESOURCE_NOT_AVAILABLE
} fw_rtos_result_t;

/* 导出常量 ------------------------------------------------------------------*/

/* 默认任务堆栈大小 */
#define FW_TASK_DEFAULT_STACK_SIZE      512

/* 默认队列大小 */
#define FW_QUEUE_DEFAULT_SIZE           8

/* 超时定义 */
#define FW_RTOS_WAIT_FOREVER            osWaitForever
#define FW_RTOS_NO_WAIT                 0

/* 导出宏 --------------------------------------------------------------------*/

/**
 * @brief 任务配置初始化宏
 */
#define FW_TASK_CONFIG_INIT(task_name, task_func, task_prio, stack_sz, arg) \
    { .name = task_name, .function = task_func, .priority = task_prio, \
      .stack_size = stack_sz, .argument = arg }

/**
 * @brief 任务句柄初始化宏
 */
#define FW_TASK_HANDLE_INIT(config) \
    { .handle = NULL, .config = config, .initialized = false, .state = FW_TASK_STATE_INACTIVE }

/* 导出函数原型 --------------------------------------------------------------*/

/**
 * @brief 初始化RTOS封装层
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_rtos_init(void);

/**
 * @brief 反初始化RTOS封装层
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_rtos_deinit(void);

/**
 * @brief 创建任务
 * @param task_handle 任务句柄指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_task_create(fw_task_handle_t* task_handle);

/**
 * @brief 删除任务
 * @param task_handle 任务句柄指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_task_delete(fw_task_handle_t* task_handle);

/**
 * @brief 挂起任务
 * @param task_handle 任务句柄指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_task_suspend(fw_task_handle_t* task_handle);

/**
 * @brief 恢复任务
 * @param task_handle 任务句柄指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_task_resume(fw_task_handle_t* task_handle);

/**
 * @brief 任务延时
 * @param delay_ms 延时时间（毫秒）
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_task_delay(uint32_t delay_ms);

/**
 * @brief 获取任务状态
 * @param task_handle 任务句柄指针
 * @param state 状态指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_task_get_state(fw_task_handle_t* task_handle, fw_task_state_t* state);

/**
 * @brief 创建消息队列
 * @param queue_handle 队列句柄指针
 * @param name 队列名称
 * @param msg_count 消息数量
 * @param msg_size 消息大小
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_queue_create(fw_queue_handle_t* queue_handle, 
                                const char* name, 
                                uint32_t msg_count, 
                                uint32_t msg_size);

/**
 * @brief 删除消息队列
 * @param queue_handle 队列句柄指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_queue_delete(fw_queue_handle_t* queue_handle);

/**
 * @brief 发送消息到队列
 * @param queue_handle 队列句柄指针
 * @param msg_ptr 消息指针
 * @param timeout 超时时间
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_queue_send(fw_queue_handle_t* queue_handle, 
                              const void* msg_ptr, 
                              uint32_t timeout);

/**
 * @brief 从队列接收消息
 * @param queue_handle 队列句柄指针
 * @param msg_ptr 消息指针
 * @param timeout 超时时间
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_queue_receive(fw_queue_handle_t* queue_handle, 
                                 void* msg_ptr, 
                                 uint32_t timeout);

/**
 * @brief 创建信号量
 * @param sem_handle 信号量句柄指针
 * @param name 信号量名称
 * @param max_count 最大计数
 * @param initial_count 初始计数
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_semaphore_create(fw_semaphore_handle_t* sem_handle,
                                    const char* name,
                                    uint32_t max_count,
                                    uint32_t initial_count);

/**
 * @brief 删除信号量
 * @param sem_handle 信号量句柄指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_semaphore_delete(fw_semaphore_handle_t* sem_handle);

/**
 * @brief 获取信号量
 * @param sem_handle 信号量句柄指针
 * @param timeout 超时时间
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_semaphore_acquire(fw_semaphore_handle_t* sem_handle, uint32_t timeout);

/**
 * @brief 释放信号量
 * @param sem_handle 信号量句柄指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_semaphore_release(fw_semaphore_handle_t* sem_handle);

/**
 * @brief 创建互斥量
 * @param mutex_handle 互斥量句柄指针
 * @param name 互斥量名称
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_mutex_create(fw_mutex_handle_t* mutex_handle, const char* name);

/**
 * @brief 删除互斥量
 * @param mutex_handle 互斥量句柄指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_mutex_delete(fw_mutex_handle_t* mutex_handle);

/**
 * @brief 获取互斥量
 * @param mutex_handle 互斥量句柄指针
 * @param timeout 超时时间
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_mutex_acquire(fw_mutex_handle_t* mutex_handle, uint32_t timeout);

/**
 * @brief 释放互斥量
 * @param mutex_handle 互斥量句柄指针
 * @retval fw_rtos_result_t RTOS结果
 */
fw_rtos_result_t fw_mutex_release(fw_mutex_handle_t* mutex_handle);

/**
 * @brief 获取系统时钟节拍
 * @retval uint32_t 当前时钟节拍
 */
uint32_t fw_rtos_get_tick_count(void);

/**
 * @brief 获取空闲任务剩余堆栈大小
 * @retval uint32_t 剩余堆栈大小
 */
uint32_t fw_rtos_get_free_heap_size(void);

#ifdef __cplusplus
}
#endif

#endif /* __FW_RTOS_H__ */
